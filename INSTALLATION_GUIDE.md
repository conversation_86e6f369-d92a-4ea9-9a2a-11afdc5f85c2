# 安裝指南

## 📋 系統要求

在開始之前，請確保你的系統已安裝以下軟體：

### 必需軟體
- **Node.js 18+** - JavaScript 運行環境
- **npm** (隨 Node.js 安裝) 或 **yarn** 或 **pnpm** - 包管理器
- **Git** - 版本控制系統

### 推薦軟體
- **VS Code** - 代碼編輯器
- **Chrome/Firefox** - 現代瀏覽器

## 🔧 安裝 Node.js

### macOS
```bash
# 使用 Homebrew (推薦)
brew install node

# 或下載官方安裝包
# https://nodejs.org/
```

### Windows
```bash
# 使用 Chocolatey
choco install nodejs

# 或下載官方安裝包
# https://nodejs.org/
```

### Linux (Ubuntu/Debian)
```bash
# 使用 NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 或使用 snap
sudo snap install node --classic
```

### 驗證安裝
```bash
node --version  # 應該顯示 v18.x.x 或更高版本
npm --version   # 應該顯示版本號
```

## 📦 初始化項目

### 方法 1: 使用現有項目文件

如果你已經有項目文件：

```bash
# 進入項目目錄
cd /path/to/your/portfolio

# 安裝依賴
npm install

# 啟動開發服務器
npm run dev
```

### 方法 2: 從頭開始創建

```bash
# 創建新的 React 項目
npm create vite@latest my-portfolio -- --template react

# 進入項目目錄
cd my-portfolio

# 安裝依賴
npm install

# 啟動開發服務器
npm run dev
```

## 🛠️ 安裝項目依賴

### 核心依賴
```bash
# 路由管理
npm install react-router-dom

# 圖標庫
npm install react-icons

# 動畫庫
npm install framer-motion

# 表單處理
npm install react-hook-form

# HTTP 請求
npm install axios

# 工具庫
npm install clsx
```

### 開發依賴
```bash
# 代碼品質工具
npm install -D eslint prettier

# 測試工具
npm install -D vitest @testing-library/react @testing-library/jest-dom @testing-library/user-event jsdom
```

## ⚙️ VS Code 設置

### 推薦擴展
安裝以下 VS Code 擴展以獲得最佳開發體驗：

```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

### VS Code 設置
創建 `.vscode/settings.json`：

```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "emmet.includeLanguages": {
    "javascript": "javascriptreact"
  },
  "files.associations": {
    "*.css": "css"
  }
}
```

## 🚀 啟動項目

### 開發模式
```bash
npm run dev
```
- 啟動開發服務器
- 默認在 http://localhost:3000 運行
- 支持熱重載

### 建構生產版本
```bash
npm run build
```
- 創建優化的生產版本
- 輸出到 `dist` 目錄

### 預覽生產版本
```bash
npm run preview
```
- 本地預覽建構後的版本

## 🔍 故障排除

### 常見問題

#### 1. Node.js 版本過舊
```bash
# 檢查版本
node --version

# 如果版本低於 18，請升級 Node.js
```

#### 2. npm 安裝失敗
```bash
# 清除 npm 緩存
npm cache clean --force

# 刪除 node_modules 和 package-lock.json
rm -rf node_modules package-lock.json

# 重新安裝
npm install
```

#### 3. 端口被占用
```bash
# 查看端口使用情況
lsof -i :3000

# 殺死占用端口的進程
kill -9 <PID>

# 或使用不同端口
npm run dev -- --port 3001
```

#### 4. 權限問題 (macOS/Linux)
```bash
# 修復 npm 權限
sudo chown -R $(whoami) ~/.npm
```

#### 5. 模組找不到
```bash
# 確保所有依賴都已安裝
npm install

# 檢查 package.json 中的依賴
cat package.json
```

### 環境變數設置

創建 `.env.local` 文件：

```env
# API 端點
VITE_API_URL=https://api.example.com

# Google Analytics (可選)
VITE_GA_ID=G-XXXXXXXXXX

# 聯絡表單服務 (可選)
VITE_FORM_ENDPOINT=https://formspree.io/f/your-form-id
```

## 📱 瀏覽器支援

### 支援的瀏覽器
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### 檢查瀏覽器兼容性
```bash
# 安裝 browserslist
npm install -D browserslist

# 查看支援的瀏覽器
npx browserslist
```

## 🔧 進階配置

### 自定義 Vite 配置
編輯 `vite.config.js`：

```javascript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true,
    host: true // 允許外部訪問
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})
```

### 配置路徑別名
```javascript
// vite.config.js
import path from 'path'

export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages')
    }
  }
})
```

## ✅ 驗證安裝

運行以下命令確保一切正常：

```bash
# 檢查 linting
npm run lint

# 運行測試 (如果有)
npm run test

# 建構項目
npm run build

# 預覽建構結果
npm run preview
```

如果所有命令都成功執行，恭喜你！項目已經成功設置完成。

## 📞 獲得幫助

如果遇到問題：

1. 檢查 [故障排除](#-故障排除) 部分
2. 查看項目的 GitHub Issues
3. 參考官方文檔：
   - [Vite 文檔](https://vitejs.dev/)
   - [React 文檔](https://react.dev/)
4. 聯絡項目維護者

---

🎉 **安裝完成！** 現在你可以開始開發你的個人作品集網站了！
