<!doctype html>
<html lang="zh-TW">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="<PERSON> Chun Yin - 個人作品集網站，展示我的技能、專案和經驗" />
    <meta name="keywords" content="前端開發, React, JavaScript, 作品集, 個人網站" />
    <meta name="author" content="<PERSON> Chun Yin" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://your-domain.com/" />
    <meta property="og:title" content="<PERSON> Chun Yin - 個人作品集" />
    <meta property="og:description" content="展示我的技能、專案和經驗的個人作品集網站" />
    <meta property="og:image" content="/og-image.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://your-domain.com/" />
    <meta property="twitter:title" content="<PERSON> Chun Yin - 個人作品集" />
    <meta property="twitter:description" content="展示我的技能、專案和經驗的個人作品集網站" />
    <meta property="twitter:image" content="/og-image.jpg" />

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <title>Chan Chun Yin - 個人作品集</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
