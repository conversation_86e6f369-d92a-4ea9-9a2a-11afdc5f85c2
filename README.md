# Leo's Portfolio Website

A modern, responsive portfolio website built with React and Bootstrap, featuring a Web 2.0 design aesthetic.

## 🚀 Features

- **Modern Design**: Web 2.0 style with gradient backgrounds, 3D effects, and smooth animations
- **Responsive Layout**: Fully responsive design that works on all devices
- **Interactive Components**: Hover effects, animations, and smooth transitions
- **Multi-page Navigation**: Home, About, Skills, Portfolio, Blog, and Contact pages
- **Contact Form**: Functional contact form with validation
- **Skills Showcase**: Interactive skill bars and technology icons
- **Project Portfolio**: Detailed project cards with modal views

## 🛠️ Technologies Used

- **Frontend**: React 18, React Router DOM
- **Styling**: Bootstrap 5, Custom CSS
- **Icons**: React Icons (Font Awesome, Heroicons)
- **Animations**: Framer Motion
- **Build Tool**: Vite
- **Package Manager**: npm

## 📋 Pages

1. **Home**: Hero section with introduction and skill preview
2. **About**: Personal background, experience timeline, and education
3. **Skills**: Detailed technical skills with proficiency levels
4. **Portfolio**: Project showcase with filtering and modal details
5. **Blog**: Blog posts and articles (coming soon)
6. **Contact**: Contact form and social media links

## 🎨 Design Features

- **Color Scheme**: Ocean teal (#0891b2) with coral orange (#f97316) accents
- **Typography**: Modern, clean fonts with proper hierarchy
- **3D Effects**: Inset/outset shadows for depth
- **Gradients**: Subtle gradients throughout the interface
- **Animations**: Smooth hover effects and transitions

## 🏗️ Installation & Setup

1. Clone the repository:
```bash
git clone https://github.com/raychan04199/My-Website.git
cd My-Website
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Build for production:
```bash
npm run build
```

## 📱 Responsive Design

The website is fully responsive and optimized for:
- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (320px - 767px)

## 🎯 Skills Highlighted

- **Frontend Development**: React, Vue, JavaScript, TypeScript
- **UI/UX Design**: Figma, Adobe XD, Responsive Design
- **Performance Optimization**: Webpack, Vite, Code Splitting
- **AI Applications**: ChatGPT API, Claude API, AI Integration

## 📧 Contact

- **Email**: <EMAIL>
- **Phone**: +852 9303-5365
- **LinkedIn**: [linkedin.com/in/leochan-483b5b211](https://linkedin.com/in/leochan-483b5b211)
- **GitHub**: [github.com/raychan04199](https://github.com/raychan04199)

## 🎓 Education

**University of the West of England**
- Bachelor's Degree in Electronic and Computer Engineering
- Bristol, United Kingdom

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

---

Built with ❤️ by Leo Chan
