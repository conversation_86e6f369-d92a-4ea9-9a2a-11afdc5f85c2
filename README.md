# Chan Chun Yin - 個人作品集網站

一個使用 React 18 和 Vite 建構的現代化個人作品集網站。

## 🚀 功能特色

- ⚛️ **React 18** - 最新的 React 功能
- ⚡ **Vite** - 快速的建構工具
- 🎨 **現代設計** - 簡潔優雅的 UI
- 📱 **響應式設計** - 支援所有裝置
- 🌙 **深色模式** - 自動切換主題
- 🎭 **動畫效果** - Framer Motion 動畫
- 🔍 **SEO 優化** - 搜尋引擎友善
- ♿ **可訪問性** - 符合 WCAG 標準

## 📄 頁面結構

- **首頁** - 個人介紹和亮點展示
- **關於我** - 詳細背景和經歷
- **技能** - 技術技能和證書
- **部落格** - 技術文章分享
- **聯絡** - 聯絡表單和資訊

## 🛠️ 技術棧

### 前端
- React 18
- React Router DOM
- Framer Motion
- React Hook Form
- React Icons

### 建構工具
- Vite
- ESLint
- Prettier

### 樣式
- CSS Custom Properties
- CSS Modules
- 響應式設計

## 🚀 快速開始

### 環境要求
- Node.js 18+
- npm 或 yarn 或 pnpm

### 安裝與運行

```bash
# 克隆專案
git clone https://github.com/your-username/portfolio.git
cd portfolio

# 安裝依賴
npm install

# 啟動開發服務器
npm run dev

# 建構生產版本
npm run build

# 預覽建構結果
npm run preview
```

### 開發命令

```bash
# 開發模式
npm run dev

# 建構
npm run build

# 預覽
npm run preview

# 代碼檢查
npm run lint

# 測試
npm run test
```

## 📁 項目結構

```
src/
├── components/          # 可重用組件
│   ├── common/         # 通用組件
│   ├── layout/         # 布局組件
│   └── ui/            # UI組件
├── pages/              # 頁面組件
├── hooks/              # 自定義Hook
├── context/            # Context提供者
├── utils/              # 工具函數
├── assets/             # 靜態資源
├── styles/             # 樣式文件
└── data/              # 靜態數據
```

## 🎨 設計系統

### 色彩方案
- **主色**: #2563eb (藍色)
- **次色**: #64748b (灰藍)
- **強調色**: #f59e0b (橙色)

### 字體
- **標題**: Poppins
- **內文**: Inter

### 間距
- 基於 8px 網格系統

## 📱 響應式設計

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## 🔧 自定義配置

### 個人資訊
編輯以下文件來自定義個人資訊：

- `src/data/personal.js` - 個人基本資訊
- `src/data/skills.js` - 技能數據
- `src/data/projects.js` - 專案數據
- `src/data/blog.js` - 部落格文章

### 主題配置
在 `src/styles/variables.css` 中修改：

- 色彩變數
- 字體設定
- 間距系統
- 動畫時間

## 🚀 部署

### Vercel (推薦)
```bash
npm install -g vercel
vercel
```

### Netlify
```bash
npm run build
# 上傳 dist 資料夾到 Netlify
```

### GitHub Pages
```bash
npm install -D gh-pages
npm run build
npx gh-pages -d dist
```

## 📈 性能優化

- 代碼分割
- 圖片懶加載
- CSS 優化
- Bundle 分析

## 🧪 測試

```bash
# 運行測試
npm run test

# 測試覆蓋率
npm run test:coverage
```

## 📝 開發指南

### 代碼風格
- 使用 ESLint 和 Prettier
- 遵循 React 最佳實踐
- 組件命名使用 PascalCase
- 文件命名使用 camelCase

### 提交規範
```
feat: 新功能
fix: 修復 bug
docs: 文檔更新
style: 代碼格式化
refactor: 重構
test: 測試相關
chore: 建構工具變動
```

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request！

## 📄 授權

MIT License

## 📞 聯絡

- 📧 Email: <EMAIL>
- 🌐 Website: https://your-website.com
- 💼 LinkedIn: https://linkedin.com/in/your-profile
- 🐙 GitHub: https://github.com/your-username

---

⭐ 如果這個專案對你有幫助，請給個星星！
