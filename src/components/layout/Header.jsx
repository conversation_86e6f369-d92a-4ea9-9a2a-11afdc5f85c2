import React, { useState, useEffect } from 'react'
import { Navbar, Nav, Container } from 'react-bootstrap'
import { Link } from 'react-router-dom'
import { useTheme } from '../../context/ThemeContext'
import { HiSun, HiMoon } from 'react-icons/hi'

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const { theme, toggleTheme } = useTheme()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <Navbar
      expand="lg"
      fixed="top"
      className="custom-navbar"
      style={{
        backgroundColor: isScrolled ? 'rgba(255, 255, 255, 0.95)' : 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(10px)',
        borderBottom: '1px solid rgba(8, 145, 178, 0.1)',
        padding: '15px 0',
        transition: 'all 0.3s ease',
        boxShadow: isScrolled ? '0 2px 20px rgba(0,0,0,0.1)' : 'none'
      }}
    >
      <Container>
        <Navbar.Brand
          as={Link}
          to="/"
          style={{
            fontSize: '1.8rem',
            fontWeight: '700',
            color: '#0891b2',
            textDecoration: 'none',
            letterSpacing: '1px',
            textShadow: '1px 1px 2px rgba(255,255,255,0.8)'
          }}
        >
          Leo's Workspace
        </Navbar.Brand>

        <Navbar.Toggle aria-controls="basic-navbar-nav" />

        <Navbar.Collapse id="basic-navbar-nav">
          <Nav className="ms-auto align-items-center">
            <Nav.Link
              as={Link}
              to="/about"
              style={{
                color: '#374151',
                fontWeight: '500',
                margin: '0 10px',
                padding: '8px 20px',
                borderRadius: '25px',
                transition: 'all 0.3s ease',
                textDecoration: 'none',
                background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                boxShadow: '2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)'
              }}
              className="nav-link-custom"
            >
              About Me
            </Nav.Link>

            <Nav.Link
              as={Link}
              to="/skills"
              style={{
                color: '#374151',
                fontWeight: '500',
                margin: '0 10px',
                padding: '8px 20px',
                borderRadius: '25px',
                transition: 'all 0.3s ease',
                textDecoration: 'none',
                background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                boxShadow: '2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)'
              }}
              className="nav-link-custom"
            >
              Skills
            </Nav.Link>

            <Nav.Link
              as={Link}
              to="/portfolio"
              style={{
                color: '#374151',
                fontWeight: '500',
                margin: '0 10px',
                padding: '8px 20px',
                borderRadius: '25px',
                transition: 'all 0.3s ease',
                textDecoration: 'none',
                background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                boxShadow: '2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)'
              }}
              className="nav-link-custom"
            >
              Portfolio
            </Nav.Link>

            <Nav.Link
              as={Link}
              to="/blog"
              style={{
                color: '#374151',
                fontWeight: '500',
                margin: '0 10px',
                padding: '8px 20px',
                borderRadius: '25px',
                transition: 'all 0.3s ease',
                textDecoration: 'none',
                background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                boxShadow: '2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)'
              }}
              className="nav-link-custom"
            >
              Blog
            </Nav.Link>

            <Nav.Link
              as={Link}
              to="/contact"
              style={{
                color: '#374151',
                fontWeight: '500',
                margin: '0 10px',
                padding: '8px 20px',
                borderRadius: '25px',
                transition: 'all 0.3s ease',
                textDecoration: 'none',
                background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                boxShadow: '2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)'
              }}
              className="nav-link-custom"
            >
              Contact
            </Nav.Link>

            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              style={{
                padding: '8px 12px',
                borderRadius: '50%',
                background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                border: 'none',
                color: '#0891b2',
                marginLeft: '15px',
                transition: 'all 0.3s ease',
                boxShadow: '2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)'
              }}
              aria-label="切換主題"
            >
              {theme === 'light' ? <HiMoon size={20} /> : <HiSun size={20} />}
            </button>
          </Nav>
        </Navbar.Collapse>
      </Container>
    </Navbar>
  )
}

export default Header
