import { useState, useEffect } from 'react'
import { Link, useLocation } from 'react-router-dom'
import Navigation from './Navigation'
import { useTheme } from '../../context/ThemeContext'
import { HiMenu, Hi<PERSON>, HiSun, HiM<PERSON> } from 'react-icons/hi'

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const { theme, toggleTheme } = useTheme()
  const location = useLocation()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    // 關閉手機選單當路由改變時
    setIsMobileMenuOpen(false)
  }, [location])

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
      isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-primary-100'
        : 'bg-transparent'
    }`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <Link
            to="/"
            className="text-2xl font-bold text-primary-600 hover:text-primary-700 transition-colors duration-300"
            style={{
              textShadow: '1px 1px 2px rgba(255,255,255,0.8)'
            }}
          >
            Chan Chun Yin
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <Navigation />
          </div>

          {/* Theme Toggle & Mobile Menu Button */}
          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="p-2 rounded-lg bg-primary-100 text-primary-600 hover:bg-primary-200 transition-all duration-300 shadow-md hover:shadow-lg"
              aria-label="切換主題"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.8), 0 2px 4px rgba(37, 99, 235, 0.2)'
              }}
            >
              {theme === 'light' ? <HiMoon size={20} /> : <HiSun size={20} />}
            </button>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden p-2 rounded-lg bg-primary-100 text-primary-600 hover:bg-primary-200 transition-all duration-300 shadow-md hover:shadow-lg"
              aria-label="開啟選單"
              style={{
                boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.8), 0 2px 4px rgba(37, 99, 235, 0.2)'
              }}
            >
              {isMobileMenuOpen ? <HiX size={24} /> : <HiMenu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden py-4 border-t border-primary-200 bg-white/95 backdrop-blur-md shadow-lg">
            <Navigation mobile />
          </div>
        )}
      </div>
    </header>
  )
}

export default Header
