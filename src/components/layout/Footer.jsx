import { Link } from 'react-router-dom'
import { HiArrowUp } from 'react-icons/hi'
import { FaGithub, FaLinkedin, FaTwi<PERSON>, FaEnvelope } from 'react-icons/fa'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  const socialLinks = [
    {
      name: 'GitHub',
      icon: FaGithub,
      url: 'https://github.com/your-username',
      color: 'hover:text-gray-900 dark:hover:text-white'
    },
    {
      name: 'LinkedIn',
      icon: FaLinkedin,
      url: 'https://linkedin.com/in/your-profile',
      color: 'hover:text-blue-600'
    },
    {
      name: 'Twitter',
      icon: FaTwitter,
      url: 'https://twitter.com/your-handle',
      color: 'hover:text-blue-400'
    },
    {
      name: '<PERSON><PERSON>',
      icon: FaEnvelope,
      url: 'mailto:<EMAIL>',
      color: 'hover:text-red-500'
    }
  ]

  const quickLinks = [
    { name: '首頁', path: '/' },
    { name: '關於我', path: '/about' },
    { name: '技能', path: '/skills' },
    { name: '部落格', path: '/blog' },
    { name: '聯絡', path: '/contact' }
  ]

  return (
    <footer className="bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand & Description */}
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">
              Chan Chun Yin
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
              熱愛前端開發的工程師，專注於創造優質的用戶體驗和現代化的網頁應用程式。
            </p>
            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => {
                const IconComponent = social.icon
                return (
                  <a
                    key={social.name}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`text-gray-500 dark:text-gray-400 transition-colors ${social.color}`}
                    aria-label={social.name}
                  >
                    <IconComponent size={20} />
                  </a>
                )
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              快速連結
            </h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    to={link.path}
                    className="text-gray-600 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
              聯絡資訊
            </h4>
            <div className="space-y-2 text-sm">
              <p className="text-gray-600 dark:text-gray-400">
                📧 <EMAIL>
              </p>
              <p className="text-gray-600 dark:text-gray-400">
                📍 台灣
              </p>
              <p className="text-gray-600 dark:text-gray-400">
                💼 開放工作機會
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-gray-500 dark:text-gray-400 text-sm">
            © {currentYear} Chan Chun Yin. All rights reserved.
          </p>
          
          {/* Back to Top Button */}
          <button
            onClick={scrollToTop}
            className="mt-4 sm:mt-0 flex items-center space-x-2 text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400 transition-colors text-sm"
            aria-label="回到頂部"
          >
            <span>回到頂部</span>
            <HiArrowUp size={16} />
          </button>
        </div>
      </div>
    </footer>
  )
}

export default Footer
