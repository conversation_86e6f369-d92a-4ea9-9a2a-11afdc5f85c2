import React from 'react'
import { Contain<PERSON>, <PERSON>, <PERSON>, But<PERSON> } from 'react-bootstrap'
import { <PERSON> } from 'react-router-dom'
import { HiArrowUp } from 'react-icons/hi'
import { FaGithub, FaLinkedin, FaEnvelope } from 'react-icons/fa'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  const socialLinks = [
    {
      name: 'GitHub',
      icon: FaGithub,
      url: 'https://github.com/raychan04199',
      color: 'hover:text-gray-900 dark:hover:text-white'
    },
    {
      name: 'LinkedIn',
      icon: FaLinkedin,
      url: 'https://linkedin.com/in/leochan-483b5b211',
      color: 'hover:text-blue-600'
    },
    {
      name: 'Email',
      icon: FaEnvelope,
      url: 'mailto:<EMAIL>',
      color: 'hover:text-red-500'
    }
  ]

  const quickLinks = [
    { name: '首頁', path: '/' },
    { name: '關於我', path: '/about' },
    { name: '技能', path: '/skills' },
    { name: '作品集', path: '/portfolio' },
    { name: '部落格', path: '/blog' },
    { name: '聯絡', path: '/contact' }
  ]

  return (
    <footer
      style={{
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
        borderTop: '1px solid rgba(8, 145, 178, 0.1)',
        boxShadow: '0 -5px 20px rgba(0,0,0,0.1)',
        marginTop: '4rem'
      }}
    >
      <Container style={{ padding: '3rem 0' }}>
        <Row>
          {/* Brand & Description */}
          <Col md={4} className="mb-4">
            <div
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                borderRadius: '20px',
                padding: '2rem',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                height: '100%'
              }}
            >
              <h3
                style={{
                  fontSize: '1.5rem',
                  fontWeight: '700',
                  color: '#0891b2',
                  textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                  marginBottom: '1rem'
                }}
              >
                Leo's Workspace
              </h3>
              <p
                style={{
                  color: '#6b7280',
                  fontSize: '0.95rem',
                  lineHeight: '1.6',
                  marginBottom: '1.5rem'
                }}
              >
                熱愛前端開發的工程師，專注於創造優質的用戶體驗和現代化的網頁應用程式。
              </p>

              {/* Social Links */}
              <div className="d-flex gap-3">
                {socialLinks.map((social) => {
                  const IconComponent = social.icon
                  return (
                    <a
                      key={social.name}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '50%',
                        background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#6b7280',
                        textDecoration: 'none',
                        transition: 'all 0.3s ease',
                        boxShadow: '2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)'
                      }}
                      aria-label={social.name}
                      className="social-link"
                    >
                      <IconComponent size={18} />
                    </a>
                  )
                })}
              </div>
            </div>
          </Col>

          {/* Quick Links */}
          <Col md={4} className="mb-4">
            <div
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                borderRadius: '20px',
                padding: '2rem',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                height: '100%'
              }}
            >
              <h4
                style={{
                  fontSize: '1.3rem',
                  fontWeight: '600',
                  color: '#0891b2',
                  textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                  marginBottom: '1.5rem'
                }}
              >
                快速連結
              </h4>
              <ul style={{ listStyle: 'none', padding: '0', margin: '0' }}>
                {quickLinks.map((link) => (
                  <li key={link.name} style={{ marginBottom: '0.75rem' }}>
                    <Link
                      to={link.path}
                      style={{
                        color: '#6b7280',
                        textDecoration: 'none',
                        fontSize: '0.95rem',
                        fontWeight: '500',
                        padding: '8px 16px',
                        borderRadius: '10px',
                        display: 'inline-block',
                        transition: 'all 0.3s ease',
                        background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                        boxShadow: '2px 2px 5px rgba(0,0,0,0.05), inset 1px 1px 2px rgba(255,255,255,0.8)'
                      }}
                      className="footer-link"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </Col>

          {/* Contact Info */}
          <Col md={4} className="mb-4">
            <div
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                borderRadius: '20px',
                padding: '2rem',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                height: '100%'
              }}
            >
              <h4
                style={{
                  fontSize: '1.3rem',
                  fontWeight: '600',
                  color: '#0891b2',
                  textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                  marginBottom: '1.5rem'
                }}
              >
                聯絡資訊
              </h4>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
                <p
                  style={{
                    color: '#6b7280',
                    fontSize: '0.95rem',
                    margin: '0',
                    padding: '8px 12px',
                    background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                    borderRadius: '8px',
                    boxShadow: 'inset 1px 1px 3px rgba(0,0,0,0.05)'
                  }}
                >
                  📧 <EMAIL>
                </p>
                <p
                  style={{
                    color: '#6b7280',
                    fontSize: '0.95rem',
                    margin: '0',
                    padding: '8px 12px',
                    background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                    borderRadius: '8px',
                    boxShadow: 'inset 1px 1px 3px rgba(0,0,0,0.05)'
                  }}
                >
                  📞 +852 9303-5365
                </p>
                <p
                  style={{
                    color: '#6b7280',
                    fontSize: '0.95rem',
                    margin: '0',
                    padding: '8px 12px',
                    background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                    borderRadius: '8px',
                    boxShadow: 'inset 1px 1px 3px rgba(0,0,0,0.05)'
                  }}
                >
                  📍 香港
                </p>
                <p
                  style={{
                    color: '#10b981',
                    fontSize: '0.95rem',
                    margin: '0',
                    padding: '8px 12px',
                    background: 'linear-gradient(145deg, #f0fdf4, #dcfce7)',
                    borderRadius: '8px',
                    boxShadow: 'inset 1px 1px 3px rgba(0,0,0,0.05)',
                    fontWeight: '600'
                  }}
                >
                  💼 開放工作機會
                </p>
              </div>
            </div>
          </Col>
        </Row>

        {/* Bottom Section */}
        <Row
          style={{
            marginTop: '2rem',
            paddingTop: '2rem',
            borderTop: '1px solid rgba(8, 145, 178, 0.2)'
          }}
        >
          <Col>
            <div
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                borderRadius: '15px',
                padding: '1.5rem',
                boxShadow: '3px 3px 10px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                flexWrap: 'wrap',
                gap: '1rem'
              }}
            >
              <p
                style={{
                  color: '#6b7280',
                  fontSize: '0.9rem',
                  margin: '0',
                  fontWeight: '500'
                }}
              >
                © {currentYear} Leo's Workspace. All rights reserved.
              </p>

              {/* Back to Top Button */}
              <Button
                onClick={scrollToTop}
                variant="outline-primary"
                size="sm"
                style={{
                  borderRadius: '20px',
                  padding: '6px 16px',
                  background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                  border: '1px solid #0891b2',
                  color: '#0891b2',
                  fontWeight: '500',
                  boxShadow: '2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)',
                  transition: 'all 0.3s ease',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}
                className="back-to-top-btn"
                aria-label="回到頂部"
              >
                <span>回到頂部</span>
                <HiArrowUp size={14} />
              </Button>
            </div>
          </Col>
        </Row>
      </Container>
    </footer>
  )
}

export default Footer
