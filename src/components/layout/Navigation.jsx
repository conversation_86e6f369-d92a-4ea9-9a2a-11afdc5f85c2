import { Link, useLocation } from 'react-router-dom'
import clsx from 'clsx'

const Navigation = ({ mobile = false }) => {
  const location = useLocation()

  const navItems = [
    { path: '/', label: '首頁', name: 'home' },
    { path: '/about', label: '關於我', name: 'about' },
    { path: '/skills', label: '技能', name: 'skills' },
    { path: '/blog', label: '部落格', name: 'blog' },
    { path: '/contact', label: '聯絡', name: 'contact' },
  ]

  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }

  const baseClasses = "font-medium transition-all duration-300 relative"
  const desktopClasses = "px-4 py-2 text-sm rounded-lg hover:text-primary-600 hover:bg-primary-50"
  const mobileClasses = "block px-4 py-2 text-base hover:text-primary-600 hover:bg-primary-50 rounded-lg"

  const activeClasses = "text-primary-600 bg-primary-100"
  const inactiveClasses = "text-gray-700"

  return (
    <nav className={mobile ? "space-y-1" : "flex items-center space-x-4"}>
      {navItems.map((item) => (
        <Link
          key={item.name}
          to={item.path}
          className={clsx(
            baseClasses,
            mobile ? mobileClasses : desktopClasses,
            isActive(item.path) ? activeClasses : inactiveClasses
          )}
        >
          {item.label}
          {/* Active indicator for desktop */}
          {!mobile && isActive(item.path) && (
            <span className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary-500 rounded-full" />
          )}
        </Link>
      ))}
    </nav>
  )
}

export default Navigation
