import { Link, useLocation } from 'react-router-dom'
import clsx from 'clsx'

const Navigation = ({ mobile = false }) => {
  const location = useLocation()

  const navItems = [
    { path: '/', label: 'HOME', name: 'home' },
    { path: '/about', label: 'ABOUT', name: 'about' },
    { path: '/skills', label: 'SKILLS', name: 'skills' },
    { path: '/blog', label: 'BLOG', name: 'blog' },
    { path: '/contact', label: 'CONTACT', name: 'contact' },
  ]

  const isActive = (path) => {
    if (path === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }

  const baseClasses = "font-medium transition-all duration-300 relative tracking-wider"
  const desktopClasses = "px-4 py-2 text-sm uppercase hover:text-primary-400"
  const mobileClasses = "block px-4 py-3 text-base uppercase hover:text-primary-400 hover:bg-white/5"

  const activeClasses = "text-primary-400"
  const inactiveClasses = "text-white/70"

  return (
    <nav className={mobile ? "space-y-1" : "flex items-center space-x-1"}>
      {navItems.map((item) => (
        <Link
          key={item.name}
          to={item.path}
          className={clsx(
            baseClasses,
            mobile ? mobileClasses : desktopClasses,
            isActive(item.path) ? activeClasses : inactiveClasses
          )}
        >
          {item.label}
          {/* Active indicator for desktop */}
          {!mobile && isActive(item.path) && (
            <span className="absolute bottom-0 left-0 right-0 h-px bg-primary-400" />
          )}
        </Link>
      ))}
    </nav>
  )
}

export default Navigation
