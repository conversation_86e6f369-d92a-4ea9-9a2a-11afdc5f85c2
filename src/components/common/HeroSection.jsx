import { motion } from 'framer-motion'
import { HiDownload, HiMail } from 'react-icons/hi'

const HeroSection = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  }

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden" style={{
      background: 'var(--gradient-hero)'
    }}>
      {/* Web 2.0 Decorative Elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-20 w-32 h-32 bg-primary-300 rounded-full blur-xl"></div>
        <div className="absolute top-40 right-32 w-24 h-24 bg-accent-300 rounded-full blur-lg"></div>
        <div className="absolute bottom-32 left-1/3 w-40 h-40 bg-primary-200 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-20 w-28 h-28 bg-accent-200 rounded-full blur-xl"></div>
      </div>

      {/* Glossy Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/20 via-transparent to-white/10"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Greeting Badge */}
          <motion.div
            className="mb-8"
            variants={itemVariants}
          >
            <span className="inline-block px-6 py-3 bg-white/80 backdrop-blur-sm text-primary-600 rounded-full text-lg font-semibold shadow-lg border border-white/50">
              👋 歡迎來到我的作品集
            </span>
          </motion.div>

          {/* Main Title - Web 2.0 Style */}
          <motion.h1
            className="text-5xl sm:text-6xl lg:text-7xl font-bold text-gray-800 mb-6"
            variants={itemVariants}
            style={{
              textShadow: '2px 2px 4px rgba(255,255,255,0.8), -1px -1px 2px rgba(0,0,0,0.1)'
            }}
          >
            Chan Chun Yin
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            className="text-2xl sm:text-3xl text-primary-600 mb-8 font-semibold"
            variants={itemVariants}
          >
            前端工程師 • UI/UX 設計師
          </motion.p>

          {/* Description */}
          <motion.p
            className="text-lg text-gray-700 mb-12 max-w-2xl mx-auto leading-relaxed"
            variants={itemVariants}
          >
            專注於創造優質的用戶體驗和現代化的網頁應用程式。
            熱愛學習新技術，致力於將創意轉化為實用的數位解決方案。
          </motion.p>

          {/* CTA Buttons - Web 2.0 Style */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            variants={itemVariants}
          >
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-4 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl relative overflow-hidden"
              style={{
                background: 'var(--gradient-primary)',
                boxShadow: '0 8px 25px rgba(37, 99, 235, 0.4), inset 0 1px 0 rgba(255,255,255,0.3)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent rounded-xl"></div>
              <HiMail className="mr-2" size={20} />
              <span className="relative z-10">聯絡我</span>
            </a>

            <a
              href="/resume.pdf"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-8 py-4 bg-white/90 text-primary-600 font-semibold rounded-xl border-2 border-primary-200 hover:bg-white hover:border-primary-400 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl backdrop-blur-sm relative overflow-hidden"
              style={{
                boxShadow: '0 4px 15px rgba(37, 99, 235, 0.2), inset 0 1px 0 rgba(255,255,255,0.8)'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-b from-white/40 to-transparent rounded-xl"></div>
              <HiDownload className="mr-2" size={20} />
              <span className="relative z-10">下載履歷</span>
            </a>
          </motion.div>

          {/* Scroll Indicator - Web 2.0 */}
          <motion.div
            className="mt-16"
            variants={itemVariants}
          >
            <div className="flex flex-col items-center">
              <span className="text-sm text-gray-600 mb-3 font-medium">
                向下滾動探索更多
              </span>
              <div className="w-6 h-10 border-2 border-primary-400 rounded-full flex justify-center bg-white/50 shadow-lg">
                <div className="w-1 h-3 bg-primary-500 rounded-full mt-2 animate-bounce"></div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default HeroSection
