import { motion } from 'framer-motion'
import { HiDownload, HiMail } from 'react-icons/hi'

const HeroSection = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  }

  return (
    <section className="min-h-screen flex items-center justify-center relative overflow-hidden bg-black">
      {/* Animated Grid Background */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(8, 145, 178, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(8, 145, 178, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px',
          animation: 'grid-move 20s linear infinite'
        }}></div>
      </div>

      {/* Subtle Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-black/50 to-black"></div>

      {/* Floating Particles */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-primary-400 rounded-full opacity-60"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animation: `float ${3 + Math.random() * 4}s ease-in-out infinite`,
              animationDelay: `${Math.random() * 2}s`
            }}
          ></div>
        ))}
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          className="max-w-6xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Main Title - SpaceX Style */}
          <motion.h1
            className="text-6xl sm:text-7xl lg:text-8xl font-black text-white mb-8 tracking-tight"
            variants={itemVariants}
            style={{ fontWeight: 900 }}
          >
            CHAN
            <br />
            <span className="text-primary-400 bg-gradient-to-r from-primary-400 to-accent-400 bg-clip-text text-transparent">
              CHUN YIN
            </span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            className="text-xl sm:text-2xl text-gray-300 mb-12 font-light tracking-wide uppercase"
            variants={itemVariants}
          >
            Frontend Engineer • Digital Innovator
          </motion.p>

          {/* Description */}
          <motion.p
            className="text-lg text-gray-400 mb-16 max-w-3xl mx-auto leading-relaxed font-light"
            variants={itemVariants}
          >
            Building the future of web experiences through cutting-edge technology
            and innovative design solutions.
          </motion.p>

          {/* CTA Buttons - Minimal SpaceX Style */}
          <motion.div
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
            variants={itemVariants}
          >
            <a
              href="/contact"
              className="group relative inline-flex items-center px-12 py-4 text-white font-medium tracking-wide uppercase transition-all duration-300 overflow-hidden"
            >
              <div className="absolute inset-0 bg-white/10 backdrop-blur-sm border border-white/20 rounded-none"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 to-accent-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <span className="relative z-10 flex items-center">
                <HiMail className="mr-3" size={18} />
                Contact
              </span>
            </a>

            <a
              href="/resume.pdf"
              target="_blank"
              rel="noopener noreferrer"
              className="group relative inline-flex items-center px-12 py-4 text-white font-medium tracking-wide uppercase transition-all duration-300 overflow-hidden"
            >
              <div className="absolute inset-0 border border-white/30 rounded-none"></div>
              <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <span className="relative z-10 flex items-center">
                <HiDownload className="mr-3" size={18} />
                Resume
              </span>
            </a>
          </motion.div>

          {/* Scroll Indicator - Minimal */}
          <motion.div
            className="mt-20"
            variants={itemVariants}
          >
            <div className="flex flex-col items-center">
              <div className="w-px h-16 bg-gradient-to-b from-transparent via-white/50 to-transparent mb-4"></div>
              <div className="w-6 h-10 border border-white/30 rounded-full flex justify-center">
                <div className="w-px h-3 bg-white/70 rounded-full mt-2 animate-bounce"></div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default HeroSection
