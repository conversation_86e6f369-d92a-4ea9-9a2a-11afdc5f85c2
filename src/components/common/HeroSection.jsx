import React from 'react'
import { Con<PERSON><PERSON>, <PERSON>, <PERSON>, Button, Badge } from 'react-bootstrap'
import { <PERSON> } from 'react-router-dom'
import { motion } from 'framer-motion'
import { HiDownload, HiMail } from 'react-icons/hi'

const HeroSection = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  }

  return (
    <section
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'relative',
        overflow: 'hidden',
        background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)'
      }}
    >
      {/* Web 2.0 Decorative Elements */}
      <div
        style={{
          position: 'absolute',
          inset: '0',
          opacity: '0.3'
        }}
      >
        <div
          style={{
            position: 'absolute',
            top: '80px',
            left: '80px',
            width: '120px',
            height: '120px',
            borderRadius: '50%',
            background: 'rgba(8, 145, 178, 0.3)',
            filter: 'blur(40px)'
          }}
        ></div>
        <div
          style={{
            position: 'absolute',
            top: '160px',
            right: '120px',
            width: '90px',
            height: '90px',
            borderRadius: '50%',
            background: 'rgba(249, 115, 22, 0.3)',
            filter: 'blur(30px)'
          }}
        ></div>
        <div
          style={{
            position: 'absolute',
            bottom: '120px',
            left: '33%',
            width: '150px',
            height: '150px',
            borderRadius: '50%',
            background: 'rgba(8, 145, 178, 0.2)',
            filter: 'blur(60px)'
          }}
        ></div>
        <div
          style={{
            position: 'absolute',
            bottom: '80px',
            right: '80px',
            width: '100px',
            height: '100px',
            borderRadius: '50%',
            background: 'rgba(249, 115, 22, 0.2)',
            filter: 'blur(40px)'
          }}
        ></div>
      </div>

      {/* Glossy Overlay */}
      <div
        style={{
          position: 'absolute',
          inset: '0',
          background: 'linear-gradient(to bottom, rgba(255,255,255,0.2), transparent, rgba(255,255,255,0.1))'
        }}
      ></div>

      <Container style={{ position: 'relative', zIndex: 10 }}>
        <Row className="justify-content-center">
          <Col lg={10}>
            <motion.div
              style={{ textAlign: 'center' }}
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Greeting Badge */}
              <motion.div
                style={{ marginBottom: '2rem' }}
                variants={itemVariants}
              >
                <Badge
                  style={{
                    padding: '12px 24px',
                    background: 'linear-gradient(145deg, rgba(255,255,255,0.9), rgba(248,250,252,0.9))',
                    color: '#0891b2',
                    fontSize: '1.1rem',
                    fontWeight: '600',
                    borderRadius: '25px',
                    boxShadow: '3px 3px 10px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)',
                    border: '1px solid rgba(255,255,255,0.5)',
                    backdropFilter: 'blur(10px)'
                  }}
                >
                  👋 歡迎來到我的作品集
                </Badge>
              </motion.div>

              {/* Main Title - Web 2.0 Style */}
              <motion.h1
                style={{
                  fontSize: 'clamp(3rem, 8vw, 5rem)',
                  fontWeight: '700',
                  color: '#374151',
                  marginBottom: '1.5rem',
                  textShadow: '2px 2px 4px rgba(255,255,255,0.8), -1px -1px 2px rgba(0,0,0,0.1)'
                }}
                variants={itemVariants}
              >
                HI, I'M LEO
              </motion.h1>

              {/* Subtitle */}
              <motion.p
                style={{
                  fontSize: 'clamp(1.2rem, 4vw, 1.8rem)',
                  color: '#0891b2',
                  marginBottom: '2rem',
                  fontWeight: '600'
                }}
                variants={itemVariants}
              >
                前端工程師 • UI/UX 設計師
              </motion.p>

              {/* Description */}
              <motion.p
                style={{
                  fontSize: '1.2rem',
                  color: '#6b7280',
                  marginBottom: '3rem',
                  maxWidth: '600px',
                  margin: '0 auto 3rem',
                  lineHeight: '1.7'
                }}
                variants={itemVariants}
              >
                專注於創造優質的用戶體驗和現代化的網頁應用程式。
                熱愛學習新技術，致力於將創意轉化為實用的數位解決方案。
              </motion.p>

              {/* CTA Buttons - Web 2.0 Style */}
              <motion.div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '1rem',
                  justifyContent: 'center',
                  alignItems: 'center',
                  marginBottom: '4rem'
                }}
                variants={itemVariants}
                className="d-sm-flex flex-sm-row"
              >
                <Button
                  as={Link}
                  to="/contact"
                  style={{
                    padding: '15px 30px',
                    background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                    border: 'none',
                    borderRadius: '25px',
                    color: 'white',
                    fontWeight: '600',
                    fontSize: '1.1rem',
                    boxShadow: '5px 5px 15px rgba(8, 145, 178, 0.4), inset 1px 1px 3px rgba(255,255,255,0.3)',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    margin: '0.5rem'
                  }}
                  className="hero-cta-primary"
                >
                  <HiMail size={20} />
                  聯絡我
                </Button>

                <Button
                  href="/resume.pdf"
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '15px 30px',
                    background: 'linear-gradient(145deg, rgba(255,255,255,0.9), rgba(248,250,252,0.9))',
                    border: '2px solid #0891b2',
                    borderRadius: '25px',
                    color: '#0891b2',
                    fontWeight: '600',
                    fontSize: '1.1rem',
                    boxShadow: '3px 3px 10px rgba(8, 145, 178, 0.2), inset 1px 1px 2px rgba(255,255,255,0.8)',
                    backdropFilter: 'blur(10px)',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem',
                    margin: '0.5rem',
                    textDecoration: 'none'
                  }}
                  className="hero-cta-secondary"
                >
                  <HiDownload size={20} />
                  下載履歷
                </Button>
              </motion.div>

              {/* Scroll Indicator - Web 2.0 */}
              <motion.div
                style={{ marginTop: '4rem' }}
                variants={itemVariants}
              >
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                  <span
                    style={{
                      fontSize: '0.9rem',
                      color: '#6b7280',
                      marginBottom: '1rem',
                      fontWeight: '500'
                    }}
                  >
                    向下滾動探索更多
                  </span>
                  <div
                    style={{
                      width: '24px',
                      height: '40px',
                      border: '2px solid #0891b2',
                      borderRadius: '20px',
                      display: 'flex',
                      justifyContent: 'center',
                      background: 'rgba(255,255,255,0.5)',
                      boxShadow: '2px 2px 8px rgba(0,0,0,0.1)'
                    }}
                  >
                    <div
                      style={{
                        width: '4px',
                        height: '12px',
                        background: '#0891b2',
                        borderRadius: '2px',
                        marginTop: '8px',
                        animation: 'bounce 2s infinite'
                      }}
                    ></div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </Col>
        </Row>
      </Container>
    </section>
  )
}

export default HeroSection
