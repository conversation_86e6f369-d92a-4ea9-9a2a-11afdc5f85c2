import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import clsx from 'clsx'

const Section = ({ 
  id, 
  title, 
  subtitle, 
  children, 
  className = '',
  containerClassName = '',
  titleClassName = '',
  contentClassName = ''
}) => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const sectionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  }

  return (
    <section 
      id={id}
      className={clsx(
        'py-16 lg:py-24',
        className
      )}
      ref={ref}
    >
      <div className={clsx(
        'container mx-auto px-4 sm:px-6 lg:px-8',
        containerClassName
      )}>
        <motion.div
          variants={sectionVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          {(title || subtitle) && (
            <motion.div 
              className={clsx(
                'text-center mb-12 lg:mb-16',
                titleClassName
              )}
              variants={itemVariants}
            >
              {title && (
                <h2 className="text-4xl lg:text-5xl font-black text-white mb-8 tracking-wider uppercase">
                  {title}
                </h2>
              )}
              {subtitle && (
                <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed font-light">
                  {subtitle}
                </p>
              )}
            </motion.div>
          )}

          {/* Section Content */}
          <motion.div 
            className={contentClassName}
            variants={itemVariants}
          >
            {children}
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Section
