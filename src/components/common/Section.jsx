import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import clsx from 'clsx'

const Section = ({ 
  id, 
  title, 
  subtitle, 
  children, 
  className = '',
  containerClassName = '',
  titleClassName = '',
  contentClassName = ''
}) => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const sectionVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: "easeOut" }
    }
  }

  return (
    <section 
      id={id}
      className={clsx(
        'py-16 lg:py-24',
        className
      )}
      ref={ref}
    >
      <div className={clsx(
        'container mx-auto px-4 sm:px-6 lg:px-8',
        containerClassName
      )}>
        <motion.div
          variants={sectionVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Section Header */}
          {(title || subtitle) && (
            <motion.div 
              className={clsx(
                'text-center mb-12 lg:mb-16',
                titleClassName
              )}
              variants={itemVariants}
            >
              {title && (
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-800 mb-4"
                    style={{
                      textShadow: '1px 1px 2px rgba(255,255,255,0.8)'
                    }}>
                  {title}
                </h2>
              )}
              {subtitle && (
                <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
                  {subtitle}
                </p>
              )}
            </motion.div>
          )}

          {/* Section Content */}
          <motion.div 
            className={contentClassName}
            variants={itemVariants}
          >
            {children}
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default Section
