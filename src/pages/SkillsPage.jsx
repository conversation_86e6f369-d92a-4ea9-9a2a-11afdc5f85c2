import Section from '../components/common/Section'

const SkillsPage = () => {
  const skills = [
    {
      category: '前端技術',
      items: [
        { name: 'React', level: 90, color: '#61dafb' },
        { name: 'JavaScript', level: 85, color: '#f7df1e' },
        { name: 'TypeScript', level: 80, color: '#3178c6' },
        { name: 'HTML/CSS', level: 95, color: '#e34f26' },
        { name: 'Vue.js', level: 75, color: '#4fc08d' },
      ]
    },
    {
      category: '工具與框架',
      items: [
        { name: 'Vite', level: 85, color: '#646cff' },
        { name: 'Webpack', level: 70, color: '#8dd6f9' },
        { name: 'Git', level: 90, color: '#f05032' },
        { name: 'Figma', level: 80, color: '#f24e1e' },
        { name: 'Tailwind CSS', level: 85, color: '#06b6d4' },
      ]
    }
  ]

  const certifications = [
    {
      name: 'React Developer Certification',
      issuer: 'Meta',
      date: '2023',
      image: '🏆'
    },
    {
      name: 'JavaScript Algorithms and Data Structures',
      issuer: 'freeCodeCamp',
      date: '2022',
      image: '📜'
    },
    {
      name: 'Frontend Web Development',
      issuer: 'Coursera',
      date: '2022',
      image: '🎓'
    }
  ]

  return (
    <div className="pt-16 lg:pt-20">
      {/* Skills Section */}
      <Section 
        title="技能專長" 
        subtitle="我的技術技能和專業能力"
        className="bg-gray-50 dark:bg-gray-900"
      >
        <div className="space-y-12">
          {skills.map((skillGroup, index) => (
            <div key={index}>
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-8 text-center">
                {skillGroup.category}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
                {skillGroup.items.map((skill, skillIndex) => (
                  <div key={skillIndex} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                    <div className="flex justify-between items-center mb-3">
                      <span className="font-medium text-gray-900 dark:text-white">
                        {skill.name}
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {skill.level}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                      <div 
                        className="h-3 rounded-full transition-all duration-1000 ease-out"
                        style={{ 
                          width: `${skill.level}%`,
                          backgroundColor: skill.color 
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </Section>

      {/* Certifications Section */}
      <Section title="證書與認證">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {certifications.map((cert, index) => (
            <div key={index} className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md text-center">
              <div className="text-4xl mb-4">{cert.image}</div>
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                {cert.name}
              </h4>
              <p className="text-primary-600 dark:text-primary-400 mb-1">
                {cert.issuer}
              </p>
              <p className="text-gray-500 dark:text-gray-400 text-sm">
                {cert.date}
              </p>
            </div>
          ))}
        </div>
      </Section>
    </div>
  )
}

export default SkillsPage
