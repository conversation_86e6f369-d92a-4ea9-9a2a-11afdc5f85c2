import React from 'react'
import { Container, Row, Col, Card, ProgressBar } from 'react-bootstrap'

const SkillsPage = () => {
  const skills = [
    {
      category: '前端技術',
      items: [
        { name: 'React', level: 90, color: '#0891b2' },
        { name: 'JavaScript', level: 85, color: '#f97316' },
        { name: 'HTML5', level: 95, color: '#10b981' },
        { name: 'CSS3', level: 90, color: '#8b5cf6' },
      ]
    },
    {
      category: '工具與框架',
      items: [
        { name: 'Vite', level: 85, color: '#155e75' },
        { name: 'Webpack', level: 70, color: '#67e8f9' },
        { name: 'Git', level: 90, color: '#ea580c' },
        { name: 'Figma', level: 80, color: '#fb923c' },
        { name: 'Tailwind CSS', level: 85, color: '#0891b2' },
      ]
    },
    {
      category: 'AI 應用',
      items: [
        { name: 'ChatGPT API', level: 85, color: '#8b5cf6' },
        { name: '<PERSON> <PERSON>', level: 80, color: '#7c3aed' },
        { name: 'AI 整合開發', level: 75, color: '#a855f7' },
        { name: 'Prompt Engineering', level: 90, color: '#9333ea' },
        { name: 'AI 工作流程', level: 85, color: '#7e22ce' },
      ]
    }
  ]

  const certifications = [
    {
      name: 'React Developer Certification',
      issuer: 'Meta',
      date: '2023',
      image: '🏆'
    },
    {
      name: 'JavaScript Algorithms and Data Structures',
      issuer: 'freeCodeCamp',
      date: '2022',
      image: '📜'
    },
    {
      name: 'Frontend Web Development',
      issuer: 'Coursera',
      date: '2022',
      image: '🎓'
    },
    {
      name: 'AI Application Development',
      issuer: 'OpenAI',
      date: '2024',
      image: '🤖'
    }
  ]

  return (
    <div style={{ paddingTop: '120px', minHeight: '100vh' }}>
      <Container>
        {/* Hero Section */}
        <Row className="mb-5">
          <Col>
            <div className="text-center mb-5">
              <h1
                style={{
                  fontSize: '3rem',
                  fontWeight: '700',
                  color: '#0891b2',
                  textShadow: '2px 2px 4px rgba(255,255,255,0.8)',
                  marginBottom: '1rem'
                }}
              >
                技能專長
              </h1>
              <p
                style={{
                  fontSize: '1.2rem',
                  color: '#6b7280',
                  fontWeight: '400'
                }}
              >
                我的技術技能和專業能力
              </p>
            </div>
          </Col>
        </Row>

        {/* Skills Sections */}
        {skills.map((skillGroup, index) => (
          <div key={index} className="mb-5">
            <Row className="mb-4">
              <Col>
                <h2
                  style={{
                    fontSize: '2.2rem',
                    fontWeight: '600',
                    color: '#0891b2',
                    textAlign: 'center',
                    textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                    marginBottom: '2rem'
                  }}
                >
                  {skillGroup.category}
                </h2>
              </Col>
            </Row>

            <Row>
              {skillGroup.items.map((skill, skillIndex) => (
                <Col md={6} key={skillIndex} className="mb-4">
                  <Card
                    style={{
                      background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                      border: 'none',
                      borderRadius: '15px',
                      boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                      padding: '1.5rem',
                      height: '100%'
                    }}
                  >
                    <Card.Body>
                      <div className="d-flex justify-content-between align-items-center mb-3">
                        <span
                          style={{
                            fontWeight: '600',
                            color: '#374151',
                            fontSize: '1.1rem'
                          }}
                        >
                          {skill.name}
                        </span>
                        <span
                          style={{
                            fontSize: '0.9rem',
                            color: skill.color,
                            fontWeight: '600'
                          }}
                        >
                          {skill.level}%
                        </span>
                      </div>

                      <div
                        style={{
                          width: '100%',
                          height: '12px',
                          background: 'linear-gradient(145deg, #e2e8f0, #cbd5e1)',
                          borderRadius: '6px',
                          boxShadow: 'inset 2px 2px 4px rgba(0,0,0,0.1)',
                          overflow: 'hidden'
                        }}
                      >
                        <div
                          style={{
                            width: `${skill.level}%`,
                            height: '100%',
                            background: `linear-gradient(90deg, ${skill.color}, ${skill.color}dd)`,
                            borderRadius: '6px',
                            boxShadow: `0 0 8px ${skill.color}60`,
                            transition: 'width 1s ease-out'
                          }}
                        />
                      </div>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          </div>
        ))}

        {/* Certifications Section */}
        <Row className="mb-4">
          <Col>
            <h2
              style={{
                fontSize: '2.2rem',
                fontWeight: '600',
                color: '#0891b2',
                textAlign: 'center',
                textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                marginBottom: '2rem'
              }}
            >
              證書與認證
            </h2>
          </Col>
        </Row>

        <Row>
          {certifications.map((cert, index) => (
            <Col lg={4} md={6} key={index} className="mb-4">
              <Card
                style={{
                  background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                  border: 'none',
                  borderRadius: '15px',
                  boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                  padding: '2rem',
                  textAlign: 'center',
                  height: '100%'
                }}
              >
                <Card.Body>
                  <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>
                    {cert.image}
                  </div>
                  <h4
                    style={{
                      fontSize: '1.3rem',
                      fontWeight: '600',
                      color: '#374151',
                      marginBottom: '0.5rem'
                    }}
                  >
                    {cert.name}
                  </h4>
                  <p
                    style={{
                      color: '#0891b2',
                      fontWeight: '500',
                      marginBottom: '0.5rem'
                    }}
                  >
                    {cert.issuer}
                  </p>
                  <p
                    style={{
                      color: '#6b7280',
                      fontSize: '0.9rem'
                    }}
                  >
                    {cert.date}
                  </p>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      </Container>
    </div>
  )
}

export default SkillsPage
