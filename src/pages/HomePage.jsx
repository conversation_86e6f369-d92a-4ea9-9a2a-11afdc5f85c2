import HeroSection from '../components/common/HeroSection'
import Section from '../components/common/Section'

const HomePage = () => {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <HeroSection />

      {/* About Preview */}
      <Section
        id="about-preview"
        className="bg-black border-t border-white/10"
        title="ABOUT"
      >
        <div className="max-w-4xl mx-auto text-center">
          <p className="text-xl text-gray-300 leading-relaxed mb-12 font-light">
            Frontend engineer passionate about creating exceptional user experiences
            through cutting-edge technology and innovative design solutions.
          </p>
          <a
            href="/about"
            className="group relative inline-flex items-center px-8 py-4 text-white font-medium tracking-wide uppercase transition-all duration-300 overflow-hidden"
          >
            <div className="absolute inset-0 border border-white/30"></div>
            <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <span className="relative z-10">Learn More</span>
          </a>
        </div>
      </Section>

      {/* Skills Preview */}
      <Section
        id="skills-preview"
        className="bg-black border-t border-white/10"
        title="CAPABILITIES"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
          <div className="text-center group">
            <div className="w-20 h-20 border border-white/20 flex items-center justify-center mx-auto mb-6 group-hover:border-primary-400 transition-all duration-500 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 to-accent-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="text-3xl relative z-10">⚛️</span>
            </div>
            <h3 className="text-lg font-bold mb-3 text-white uppercase tracking-wider">Frontend Development</h3>
            <p className="text-gray-400 font-light">React, Vue, JavaScript, TypeScript</p>
          </div>

          <div className="text-center group">
            <div className="w-20 h-20 border border-white/20 flex items-center justify-center mx-auto mb-6 group-hover:border-accent-400 transition-all duration-500 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-accent-500/10 to-primary-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="text-3xl relative z-10">🎨</span>
            </div>
            <h3 className="text-lg font-bold mb-3 text-white uppercase tracking-wider">UI/UX Design</h3>
            <p className="text-gray-400 font-light">Figma, Adobe XD, Responsive Design</p>
          </div>

          <div className="text-center group">
            <div className="w-20 h-20 border border-white/20 flex items-center justify-center mx-auto mb-6 group-hover:border-primary-400 transition-all duration-500 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-primary-500/10 to-accent-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="text-3xl relative z-10">🚀</span>
            </div>
            <h3 className="text-lg font-bold mb-3 text-white uppercase tracking-wider">Performance</h3>
            <p className="text-gray-400 font-light">Optimization, Webpack, Vite</p>
          </div>
        </div>
      </Section>

      {/* Contact CTA */}
      <Section
        id="contact-cta"
        className="bg-black border-t border-white/10 text-white relative overflow-hidden"
        title="LET'S WORK TOGETHER"
      >
        <div className="text-center relative z-10">
          <p className="text-xl mb-12 text-gray-300 font-light max-w-2xl mx-auto">
            Ready to build something extraordinary? Let's discuss your next project.
          </p>
          <a
            href="/contact"
            className="group relative inline-flex items-center px-12 py-4 text-white font-medium tracking-wide uppercase transition-all duration-300 overflow-hidden"
          >
            <div className="absolute inset-0 bg-white/10 backdrop-blur-sm border border-white/20"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 to-accent-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <span className="relative z-10">Get In Touch</span>
          </a>
        </div>
      </Section>
    </div>
  )
}

export default HomePage
