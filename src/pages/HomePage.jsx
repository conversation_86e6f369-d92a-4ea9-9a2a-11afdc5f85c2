import HeroSection from '../components/common/HeroSection'
import Section from '../components/common/Section'

const HomePage = () => {
  return (
    <div className="pt-16 lg:pt-20">
      {/* Hero Section */}
      <HeroSection />

      {/* About Preview */}
      <Section
        id="about-preview"
        className="bg-gradient-to-br from-white/80 to-primary-50/50 backdrop-blur-sm"
        title="關於我"
      >
        <div className="max-w-3xl mx-auto text-center">
          <p className="text-lg text-gray-700 leading-relaxed mb-8">
            我是一位熱愛前端開發的工程師，專注於創造優質的用戶體驗和現代化的網頁應用程式。
            擁有豐富的 React、JavaScript 和現代前端技術經驗。
          </p>
          <a
            href="/about"
            className="inline-flex items-center px-8 py-3 text-white font-semibold rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl relative overflow-hidden"
            style={{
              background: 'var(--gradient-primary)',
              boxShadow: '0 6px 20px rgba(37, 99, 235, 0.3), inset 0 1px 0 rgba(255,255,255,0.3)'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent rounded-xl"></div>
            <span className="relative z-10">了解更多</span>
          </a>
        </div>
      </Section>

      {/* Skills Preview */}
      <Section
        id="skills-preview"
        className="bg-gradient-to-br from-primary-50/50 to-accent-50/50"
        title="技能專長"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center group">
            <div className="w-20 h-20 bg-gradient-to-br from-primary-400 to-primary-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg relative overflow-hidden"
                 style={{
                   boxShadow: '0 8px 25px rgba(37, 99, 235, 0.3), inset 0 1px 0 rgba(255,255,255,0.3)'
                 }}>
              <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent rounded-2xl"></div>
              <span className="text-3xl relative z-10">⚛️</span>
            </div>
            <h3 className="text-xl font-bold mb-2 text-gray-800">前端開發</h3>
            <p className="text-gray-600">React, Vue, JavaScript, TypeScript</p>
          </div>

          <div className="text-center group">
            <div className="w-20 h-20 bg-gradient-to-br from-accent-400 to-accent-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg relative overflow-hidden"
                 style={{
                   boxShadow: '0 8px 25px rgba(249, 115, 22, 0.3), inset 0 1px 0 rgba(255,255,255,0.3)'
                 }}>
              <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent rounded-2xl"></div>
              <span className="text-3xl relative z-10">🎨</span>
            </div>
            <h3 className="text-xl font-bold mb-2 text-gray-800">UI/UX 設計</h3>
            <p className="text-gray-600">Figma, Adobe XD, 響應式設計</p>
          </div>

          <div className="text-center group">
            <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-all duration-300 shadow-lg relative overflow-hidden"
                 style={{
                   boxShadow: '0 8px 25px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255,255,255,0.3)'
                 }}>
              <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent rounded-2xl"></div>
              <span className="text-3xl relative z-10">🚀</span>
            </div>
            <h3 className="text-xl font-bold mb-2 text-gray-800">性能優化</h3>
            <p className="text-gray-600">Webpack, Vite, 代碼分割</p>
          </div>
        </div>
      </Section>

      {/* Contact CTA */}
      <Section
        id="contact-cta"
        className="bg-gradient-to-br from-primary-500 to-primary-600 text-white relative overflow-hidden"
        title="讓我們一起合作"
        style={{
          boxShadow: 'inset 0 1px 0 rgba(255,255,255,0.2)'
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-white/10 to-transparent"></div>
        <div className="text-center relative z-10">
          <p className="text-xl mb-8 text-primary-100">
            有專案想法或工作機會？歡迎與我聯絡！
          </p>
          <a
            href="/contact"
            className="inline-flex items-center px-8 py-4 bg-white text-primary-600 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl relative overflow-hidden"
            style={{
              boxShadow: '0 6px 20px rgba(255,255,255,0.3), inset 0 1px 0 rgba(255,255,255,0.8)'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-b from-white/40 to-transparent rounded-xl"></div>
            <span className="relative z-10">聯絡我</span>
          </a>
        </div>
      </Section>
    </div>
  )
}

export default HomePage
