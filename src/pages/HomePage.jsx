import HeroSection from '../components/common/HeroSection'
import Section from '../components/common/Section'

const HomePage = () => {
  return (
    <div className="pt-16 lg:pt-20">
      {/* Hero Section */}
      <HeroSection />
      
      {/* About Preview */}
      <Section 
        id="about-preview" 
        className="bg-gray-50 dark:bg-gray-900"
        title="關於我"
      >
        <div className="max-w-3xl mx-auto text-center">
          <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed mb-8">
            我是一位熱愛前端開發的工程師，專注於創造優質的用戶體驗和現代化的網頁應用程式。
            擁有豐富的 React、JavaScript 和現代前端技術經驗。
          </p>
          <a 
            href="/about" 
            className="inline-flex items-center px-6 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors"
          >
            了解更多
          </a>
        </div>
      </Section>

      {/* Skills Preview */}
      <Section 
        id="skills-preview" 
        title="技能專長"
      >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">⚛️</span>
            </div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">前端開發</h3>
            <p className="text-gray-600 dark:text-gray-300">React, Vue, JavaScript, TypeScript</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🎨</span>
            </div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">UI/UX 設計</h3>
            <p className="text-gray-600 dark:text-gray-300">Figma, Adobe XD, 響應式設計</p>
          </div>
          
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🚀</span>
            </div>
            <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">性能優化</h3>
            <p className="text-gray-600 dark:text-gray-300">Webpack, Vite, 代碼分割</p>
          </div>
        </div>
      </Section>

      {/* Contact CTA */}
      <Section 
        id="contact-cta" 
        className="bg-primary-600 text-white"
        title="讓我們一起合作"
      >
        <div className="text-center">
          <p className="text-xl mb-8 text-primary-100">
            有專案想法或工作機會？歡迎與我聯絡！
          </p>
          <a 
            href="/contact" 
            className="inline-flex items-center px-8 py-4 bg-white text-primary-600 font-medium rounded-lg hover:bg-gray-100 transition-colors"
          >
            聯絡我
          </a>
        </div>
      </Section>
    </div>
  )
}

export default HomePage
