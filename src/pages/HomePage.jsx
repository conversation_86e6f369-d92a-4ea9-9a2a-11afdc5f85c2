import React from 'react'
import { Container, Row, Col, <PERSON>, Button } from 'react-bootstrap'
import { Link } from 'react-router-dom'
import HeroSection from '../components/common/HeroSection'

const HomePage = () => {
  return (
    <div style={{ paddingTop: '120px' }}>
      {/* Hero Section */}
      <HeroSection />

      {/* About Preview */}
      <Container style={{ padding: '4rem 0' }}>
        <Row className="mb-5">
          <Col>
            <div className="text-center mb-5">
              <h2
                style={{
                  fontSize: '2.5rem',
                  fontWeight: '700',
                  color: '#0891b2',
                  textShadow: '2px 2px 4px rgba(255,255,255,0.8)',
                  marginBottom: '3rem'
                }}
              >
                關於我
              </h2>
            </div>
          </Col>
        </Row>

        <Row className="justify-content-center">
          <Col lg={8}>
            <Card
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                border: 'none',
                borderRadius: '25px',
                boxShadow: '8px 8px 25px rgba(0,0,0,0.1), inset 2px 2px 5px rgba(255,255,255,0.8)',
                padding: '3rem',
                textAlign: 'center'
              }}
            >
              <Card.Body>
                <p
                  style={{
                    fontSize: '1.2rem',
                    color: '#4b5563',
                    lineHeight: '1.8',
                    marginBottom: '2.5rem'
                  }}
                >
                  我是一位熱愛前端開發的工程師，專注於創造優質的用戶體驗和現代化的網頁應用程式。
                  擁有豐富的 React、JavaScript 和現代前端技術經驗。
                </p>

                <Button
                  as={Link}
                  to="/about"
                  style={{
                    padding: '12px 30px',
                    background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                    border: 'none',
                    borderRadius: '25px',
                    color: 'white',
                    fontWeight: '600',
                    fontSize: '1.1rem',
                    boxShadow: '5px 5px 15px rgba(0,0,0,0.2), inset 1px 1px 3px rgba(255,255,255,0.3)',
                    transition: 'all 0.3s ease'
                  }}
                  className="cta-button"
                >
                  了解更多
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Skills Preview */}
      <Container style={{ padding: '4rem 0' }}>
        <Row className="mb-5">
          <Col>
            <div className="text-center mb-5">
              <h2
                style={{
                  fontSize: '2.5rem',
                  fontWeight: '700',
                  color: '#0891b2',
                  textShadow: '2px 2px 4px rgba(255,255,255,0.8)',
                  marginBottom: '3rem'
                }}
              >
                技能專長
              </h2>
            </div>
          </Col>
        </Row>

        <Row>
          <Col lg={3} md={6} className="mb-4">
            <Card
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                border: 'none',
                borderRadius: '20px',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                padding: '2rem',
                textAlign: 'center',
                height: '100%',
                transition: 'all 0.3s ease'
              }}
              className="skill-card"
            >
              <Card.Body>
                <div
                  style={{
                    width: '80px',
                    height: '80px',
                    borderRadius: '20px',
                    background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 1.5rem',
                    boxShadow: '5px 5px 15px rgba(8, 145, 178, 0.3), inset 1px 1px 3px rgba(255,255,255,0.3)',
                    fontSize: '2rem',
                    transition: 'all 0.3s ease'
                  }}
                  className="skill-icon"
                >
                  ⚛️
                </div>
                <h3
                  style={{
                    fontSize: '1.3rem',
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '0.75rem'
                  }}
                >
                  前端開發
                </h3>
                <p
                  style={{
                    color: '#6b7280',
                    fontSize: '0.95rem',
                    margin: '0'
                  }}
                >
                  React, HTML, CSS, JavaScript, Python
                </p>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-4">
            <Card
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                border: 'none',
                borderRadius: '20px',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                padding: '2rem',
                textAlign: 'center',
                height: '100%',
                transition: 'all 0.3s ease'
              }}
              className="skill-card"
            >
              <Card.Body>
                <div
                  style={{
                    width: '80px',
                    height: '80px',
                    borderRadius: '20px',
                    background: 'linear-gradient(145deg, #f97316, #ea580c)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 1.5rem',
                    boxShadow: '5px 5px 15px rgba(249, 115, 22, 0.3), inset 1px 1px 3px rgba(255,255,255,0.3)',
                    fontSize: '2rem',
                    transition: 'all 0.3s ease'
                  }}
                  className="skill-icon"
                >
                  🎨
                </div>
                <h3
                  style={{
                    fontSize: '1.3rem',
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '0.75rem'
                  }}
                >
                  UI/UX 設計
                </h3>
                <p
                  style={{
                    color: '#6b7280',
                    fontSize: '0.95rem',
                    margin: '0'
                  }}
                >
                  Figma, Adobe XD, 響應式設計
                </p>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-4">
            <Card
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                border: 'none',
                borderRadius: '20px',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                padding: '2rem',
                textAlign: 'center',
                height: '100%',
                transition: 'all 0.3s ease'
              }}
              className="skill-card"
            >
              <Card.Body>
                <div
                  style={{
                    width: '80px',
                    height: '80px',
                    borderRadius: '20px',
                    background: 'linear-gradient(145deg, #10b981, #059669)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 1.5rem',
                    boxShadow: '5px 5px 15px rgba(16, 185, 129, 0.3), inset 1px 1px 3px rgba(255,255,255,0.3)',
                    fontSize: '2rem',
                    transition: 'all 0.3s ease'
                  }}
                  className="skill-icon"
                >
                  🚀
                </div>
                <h3
                  style={{
                    fontSize: '1.3rem',
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '0.75rem'
                  }}
                >
                  性能優化
                </h3>
                <p
                  style={{
                    color: '#6b7280',
                    fontSize: '0.95rem',
                    margin: '0'
                  }}
                >
                  Webpack, Vite, 代碼分割
                </p>
              </Card.Body>
            </Card>
          </Col>

          <Col lg={3} md={6} className="mb-4">
            <Card
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                border: 'none',
                borderRadius: '20px',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                padding: '2rem',
                textAlign: 'center',
                height: '100%',
                transition: 'all 0.3s ease'
              }}
              className="skill-card"
            >
              <Card.Body>
                <div
                  style={{
                    width: '80px',
                    height: '80px',
                    borderRadius: '20px',
                    background: 'linear-gradient(145deg, #8b5cf6, #7c3aed)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto 1.5rem',
                    boxShadow: '5px 5px 15px rgba(139, 92, 246, 0.3), inset 1px 1px 3px rgba(255,255,255,0.3)',
                    fontSize: '2rem',
                    transition: 'all 0.3s ease'
                  }}
                  className="skill-icon"
                >
                  🤖
                </div>
                <h3
                  style={{
                    fontSize: '1.3rem',
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '0.75rem'
                  }}
                >
                  AI 應用
                </h3>
                <p
                  style={{
                    color: '#6b7280',
                    fontSize: '0.95rem',
                    margin: '0'
                  }}
                >
                  ChatGPT, Claude, AI 整合開發
                </p>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>

      {/* Contact CTA */}
      <Container style={{ padding: '4rem 0' }}>
        <Row className="justify-content-center">
          <Col lg={8}>
            <Card
              style={{
                background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                border: 'none',
                borderRadius: '25px',
                boxShadow: '8px 8px 25px rgba(8, 145, 178, 0.3), inset 2px 2px 5px rgba(255,255,255,0.2)',
                padding: '3rem',
                textAlign: 'center',
                color: 'white',
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              {/* 裝飾性背景元素 */}
              <div
                style={{
                  position: 'absolute',
                  top: '-50px',
                  right: '-50px',
                  width: '150px',
                  height: '150px',
                  borderRadius: '50%',
                  background: 'rgba(255,255,255,0.1)',
                  filter: 'blur(40px)'
                }}
              ></div>
              <div
                style={{
                  position: 'absolute',
                  bottom: '-30px',
                  left: '-30px',
                  width: '100px',
                  height: '100px',
                  borderRadius: '50%',
                  background: 'rgba(255,255,255,0.1)',
                  filter: 'blur(30px)'
                }}
              ></div>

              <Card.Body style={{ position: 'relative', zIndex: 2 }}>
                <h2
                  style={{
                    fontSize: '2.2rem',
                    fontWeight: '700',
                    marginBottom: '1.5rem',
                    textShadow: '1px 1px 2px rgba(0,0,0,0.2)'
                  }}
                >
                  讓我們一起合作
                </h2>

                <p
                  style={{
                    fontSize: '1.2rem',
                    marginBottom: '2.5rem',
                    opacity: '0.9',
                    lineHeight: '1.6'
                  }}
                >
                  有專案想法或工作機會？歡迎與我聯絡！
                </p>

                <Button
                  as={Link}
                  to="/contact"
                  style={{
                    padding: '15px 35px',
                    background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                    border: 'none',
                    borderRadius: '25px',
                    color: '#0891b2',
                    fontWeight: '600',
                    fontSize: '1.1rem',
                    boxShadow: '5px 5px 15px rgba(0,0,0,0.2), inset 1px 1px 3px rgba(255,255,255,0.8)',
                    transition: 'all 0.3s ease'
                  }}
                  className="contact-cta-button"
                >
                  聯絡我
                </Button>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  )
}

export default HomePage
