import React, { useState } from 'react'
import { Container, Row, Col, Card, Form, Button } from 'react-bootstrap'
import { HiMail, HiPhone, HiLocationMarker } from 'react-icons/hi'
import { FaGithub, FaLinkedin } from 'react-icons/fa'

const ContactPage = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    // 這裡可以添加表單提交邏輯
    console.log('Form submitted:', formData)
    alert('感謝您的訊息！我會盡快回覆您。')
    setFormData({ name: '', email: '', subject: '', message: '' })
  }

  const contactInfo = [
    {
      icon: HiMail,
      title: '電子郵件',
      value: '<EMAIL>',
      link: 'mailto:<EMAIL>'
    },
    {
      icon: HiP<PERSON>,
      title: '電話',
      value: '+852 9303-5365',
      link: 'tel:+85293035365'
    },
    {
      icon: HiLocationMarker,
      title: '位置',
      value: '香港',
      link: null
    }
  ]

  const socialLinks = [
    {
      name: 'GitHub',
      icon: FaGithub,
      url: 'https://github.com/raychan04199',
      color: 'hover:text-gray-900 dark:hover:text-white'
    },
    {
      name: 'LinkedIn',
      icon: FaLinkedin,
      url: 'https://linkedin.com/in/leochan-483b5b211',
      color: 'hover:text-blue-600'
    }
  ]

  return (
    <div style={{ paddingTop: '120px', minHeight: '100vh' }}>
      <Container>
        {/* Hero Section */}
        <Row className="mb-5">
          <Col>
            <div className="text-center mb-5">
              <h1
                style={{
                  fontSize: '3rem',
                  fontWeight: '700',
                  color: '#0891b2',
                  textShadow: '2px 2px 4px rgba(255,255,255,0.8)',
                  marginBottom: '1rem'
                }}
              >
                聯絡我
              </h1>
              <p
                style={{
                  fontSize: '1.2rem',
                  color: '#6b7280',
                  fontWeight: '400'
                }}
              >
                有專案想法或工作機會？歡迎與我聯絡！
              </p>
            </div>
          </Col>
        </Row>

        <Row>
          {/* Contact Information */}
          <Col lg={6} className="mb-4">
            <div className="mb-4">
              <h2
                style={{
                  fontSize: '2rem',
                  fontWeight: '600',
                  color: '#0891b2',
                  textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                  marginBottom: '2rem'
                }}
              >
                聯絡資訊
              </h2>

              <div className="d-flex flex-column gap-3">
                {contactInfo.map((info, index) => {
                  const IconComponent = info.icon
                  const content = (
                    <Card
                      style={{
                        background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                        border: 'none',
                        borderRadius: '15px',
                        boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                        padding: '1rem',
                        transition: 'all 0.3s ease'
                      }}
                      className="contact-card"
                    >
                      <Card.Body className="d-flex align-items-center">
                        <div
                          style={{
                            width: '50px',
                            height: '50px',
                            borderRadius: '50%',
                            background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            marginRight: '1rem',
                            boxShadow: '3px 3px 8px rgba(0,0,0,0.2)'
                          }}
                        >
                          <IconComponent style={{ color: 'white' }} size={24} />
                        </div>
                        <div>
                          <h4
                            style={{
                              fontWeight: '600',
                              color: '#374151',
                              marginBottom: '0.25rem'
                            }}
                          >
                            {info.title}
                          </h4>
                          <p
                            style={{
                              color: '#6b7280',
                              margin: '0'
                            }}
                          >
                            {info.value}
                          </p>
                        </div>
                      </Card.Body>
                    </Card>
                  )

                  return info.link ? (
                    <a key={index} href={info.link} style={{ textDecoration: 'none' }}>
                      {content}
                    </a>
                  ) : (
                    <div key={index}>
                      {content}
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Social Links */}
            <div className="mb-4">
              <h3
                style={{
                  fontSize: '1.5rem',
                  fontWeight: '600',
                  color: '#0891b2',
                  textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                  marginBottom: '1.5rem'
                }}
              >
                社交媒體
              </h3>
              <div className="d-flex gap-3">
                {socialLinks.map((social) => {
                  const IconComponent = social.icon
                  return (
                    <a
                      key={social.name}
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{
                        width: '50px',
                        height: '50px',
                        borderRadius: '50%',
                        background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#6b7280',
                        textDecoration: 'none',
                        transition: 'all 0.3s ease',
                        boxShadow: '3px 3px 8px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)'
                      }}
                      className="social-btn"
                      aria-label={social.name}
                    >
                      <IconComponent size={24} />
                    </a>
                  )
                })}
              </div>
            </div>

            {/* Additional Info */}
            <Card
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                border: 'none',
                borderRadius: '15px',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                padding: '1.5rem'
              }}
            >
              <Card.Body>
                <h4
                  style={{
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '1rem'
                  }}
                >
                  工作狀態
                </h4>
                <p
                  style={{
                    color: '#6b7280',
                    lineHeight: '1.6',
                    marginBottom: '1rem'
                  }}
                >
                  目前開放新的工作機會和專案合作。歡迎討論前端開發、UI/UX 設計相關的專案。
                </p>
                <div className="d-flex align-items-center">
                  <div
                    style={{
                      width: '12px',
                      height: '12px',
                      borderRadius: '50%',
                      background: '#10b981',
                      marginRight: '0.5rem',
                      animation: 'pulse 2s infinite'
                    }}
                  ></div>
                  <span
                    style={{
                      color: '#10b981',
                      fontWeight: '600'
                    }}
                  >
                    可接案
                  </span>
                </div>
              </Card.Body>
            </Card>
          </Col>

          {/* Contact Form */}
          <Col lg={6}>
            <Card
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                border: 'none',
                borderRadius: '20px',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                padding: '2rem'
              }}
            >
              <Card.Body>
                <h2
                  style={{
                    fontSize: '2rem',
                    fontWeight: '600',
                    color: '#0891b2',
                    textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                    marginBottom: '2rem'
                  }}
                >
                  發送訊息
                </h2>

                <Form onSubmit={handleSubmit}>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label
                          style={{
                            fontWeight: '600',
                            color: '#374151',
                            marginBottom: '0.5rem'
                          }}
                        >
                          姓名 *
                        </Form.Label>
                        <Form.Control
                          type="text"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                          required
                          placeholder="您的姓名"
                          style={{
                            padding: '12px 16px',
                            border: '1px solid #d1d5db',
                            borderRadius: '10px',
                            background: 'linear-gradient(145deg, #ffffff, #f9fafb)',
                            boxShadow: 'inset 2px 2px 4px rgba(0,0,0,0.05)',
                            transition: 'all 0.3s ease'
                          }}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label
                          style={{
                            fontWeight: '600',
                            color: '#374151',
                            marginBottom: '0.5rem'
                          }}
                        >
                          電子郵件 *
                        </Form.Label>
                        <Form.Control
                          type="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          required
                          placeholder="<EMAIL>"
                          style={{
                            padding: '12px 16px',
                            border: '1px solid #d1d5db',
                            borderRadius: '10px',
                            background: 'linear-gradient(145deg, #ffffff, #f9fafb)',
                            boxShadow: 'inset 2px 2px 4px rgba(0,0,0,0.05)',
                            transition: 'all 0.3s ease'
                          }}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label
                      style={{
                        fontWeight: '600',
                        color: '#374151',
                        marginBottom: '0.5rem'
                      }}
                    >
                      主題 *
                    </Form.Label>
                    <Form.Control
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      placeholder="訊息主題"
                      style={{
                        padding: '12px 16px',
                        border: '1px solid #d1d5db',
                        borderRadius: '10px',
                        background: 'linear-gradient(145deg, #ffffff, #f9fafb)',
                        boxShadow: 'inset 2px 2px 4px rgba(0,0,0,0.05)',
                        transition: 'all 0.3s ease'
                      }}
                    />
                  </Form.Group>

                  <Form.Group className="mb-4">
                    <Form.Label
                      style={{
                        fontWeight: '600',
                        color: '#374151',
                        marginBottom: '0.5rem'
                      }}
                    >
                      訊息 *
                    </Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={6}
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      placeholder="請輸入您的訊息..."
                      style={{
                        padding: '12px 16px',
                        border: '1px solid #d1d5db',
                        borderRadius: '10px',
                        background: 'linear-gradient(145deg, #ffffff, #f9fafb)',
                        boxShadow: 'inset 2px 2px 4px rgba(0,0,0,0.05)',
                        resize: 'none',
                        transition: 'all 0.3s ease'
                      }}
                    />
                  </Form.Group>

                  <Button
                    type="submit"
                    style={{
                      width: '100%',
                      padding: '15px',
                      background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                      border: 'none',
                      borderRadius: '15px',
                      color: 'white',
                      fontWeight: '600',
                      fontSize: '1.1rem',
                      boxShadow: '5px 5px 15px rgba(0,0,0,0.2), inset 1px 1px 3px rgba(255,255,255,0.3)',
                      transition: 'all 0.3s ease'
                    }}
                    className="submit-btn"
                  >
                    發送訊息
                  </Button>
                </Form>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  )
}

export default ContactPage
