import React, { useState } from 'react'
import { Container, Row, <PERSON>, <PERSON>, But<PERSON>, Badge, Modal } from 'react-bootstrap'
import { FaGithub, FaExternalLinkAlt, FaReact, FaVuejs, FaJs, FaHtml5, FaCss3Alt, FaNodeJs, FaPython } from 'react-icons/fa'
import { SiTailwindcss, SiVite, SiTypescript, SiFigma } from 'react-icons/si'

const PortfolioPage = () => {
  const [showModal, setShowModal] = useState(false)
  const [selectedProject, setSelectedProject] = useState(null)

  const projects = [
    {
      id: 1,
      title: "個人作品集網站",
      description: "使用 React 和 Bootstrap 打造的現代化個人作品集網站，採用 Web 2.0 設計風格。",
      image: "🌐",
      category: "Web Development",
      technologies: ["React", "HTML5", "CSS3", "JavaScript"],
      githubUrl: "https://github.com/raychan04199/portfolio",
      liveUrl: "http://localhost:3000",
      features: [
        "響應式設計",
        "Web 2.0 視覺效果",
        "動畫和互動效果",
        "多頁面導航"
      ],
      status: "已完成"
    },
    {
      id: 2,
      title: "電商購物平台",
      description: "全功能的電商網站，包含商品展示、購物車、結帳流程等完整功能。",
      image: "🛒",
      category: "E-commerce",
      technologies: ["React", "HTML5", "CSS3", "JavaScript", "Python"],
      githubUrl: "https://github.com/raychan04199/ecommerce",
      liveUrl: "https://shop.example.com",
      features: [
        "商品管理系統",
        "購物車功能",
        "用戶認證",
        "支付整合"
      ],
      status: "開發中"
    },
    {
      id: 3,
      title: "任務管理應用",
      description: "簡潔高效的任務管理工具，支援拖拽排序、分類標籤、進度追蹤等功能。",
      image: "📋",
      category: "Productivity",
      technologies: ["React", "HTML5", "CSS3", "JavaScript"],
      githubUrl: "https://github.com/raychan04199/task-manager",
      liveUrl: "https://tasks.example.com",
      features: [
        "拖拽排序",
        "分類管理",
        "進度追蹤",
        "數據可視化"
      ],
      status: "已完成"
    },
    {
      id: 4,
      title: "天氣預報應用",
      description: "美觀的天氣預報應用，提供即時天氣資訊、未來預報、多城市管理等功能。",
      image: "🌤️",
      category: "Mobile App",
      technologies: ["Python", "HTML5", "CSS3", "JavaScript"],
      githubUrl: "https://github.com/raychan04199/weather-app",
      liveUrl: "https://weather.example.com",
      features: [
        "即時天氣資訊",
        "7天預報",
        "多城市管理",
        "圖表展示"
      ],
      status: "已完成"
    },
    {
      id: 5,
      title: "部落格管理系統",
      description: "功能完整的部落格平台，支援文章編輯、分類管理、評論系統等功能。",
      image: "📝",
      category: "CMS",
      technologies: ["React", "Node.js", "Express", "MongoDB"],
      githubUrl: "https://github.com/raychan04199/blog-cms",
      liveUrl: "https://blog.example.com",
      features: [
        "富文本編輯器",
        "分類標籤系統",
        "評論管理",
        "SEO 優化"
      ],
      status: "規劃中"
    },
    {
      id: 6,
      title: "數據可視化儀表板",
      description: "互動式數據儀表板，提供多種圖表類型、實時數據更新、自定義配置等功能。",
      image: "📊",
      category: "Data Visualization",
      technologies: ["React", "D3.js", "Chart.js", "TypeScript"],
      githubUrl: "https://github.com/raychan04199/dashboard",
      liveUrl: "https://dashboard.example.com",
      features: [
        "多種圖表類型",
        "實時數據更新",
        "自定義配置",
        "數據導出"
      ],
      status: "開發中"
    }
  ]

  const categories = ["全部", "Web Development", "E-commerce", "Productivity", "Mobile App", "CMS", "Data Visualization"]
  const [selectedCategory, setSelectedCategory] = useState("全部")

  const filteredProjects = selectedCategory === "全部" 
    ? projects 
    : projects.filter(project => project.category === selectedCategory)

  const getTechIcon = (tech) => {
    const icons = {
      "React": <FaReact color="#61DAFB" />,
      "Vue.js": <FaVuejs color="#4FC08D" />,
      "JavaScript": <FaJs color="#F7DF1E" />,
      "TypeScript": <SiTypescript color="#3178C6" />,
      "HTML5": <FaHtml5 color="#E34F26" />,
      "CSS3": <FaCss3Alt color="#1572B6" />,
      "Python": <FaPython color="#3776AB" />,
      "Node.js": <FaNodeJs color="#339933" />,
      "Bootstrap": <span style={{color: "#7952B3"}}>B</span>,
      "Tailwind CSS": <SiTailwindcss color="#06B6D4" />,
      "Vite": <SiVite color="#646CFF" />,
      "Figma": <SiFigma color="#F24E1E" />
    }
    return icons[tech] || <span>{tech}</span>
  }

  const getStatusColor = (status) => {
    switch(status) {
      case "已完成": return "#10b981"
      case "開發中": return "#f59e0b"
      case "規劃中": return "#6b7280"
      default: return "#6b7280"
    }
  }

  const handleProjectClick = (project) => {
    setSelectedProject(project)
    setShowModal(true)
  }

  return (
    <div style={{ paddingTop: '120px', minHeight: '100vh' }}>
      <Container>
        {/* Hero Section */}
        <Row className="mb-5">
          <Col>
            <div className="text-center mb-5">
              <h1 
                style={{
                  fontSize: '3rem',
                  fontWeight: '700',
                  color: '#0891b2',
                  textShadow: '2px 2px 4px rgba(255,255,255,0.8)',
                  marginBottom: '1rem'
                }}
              >
                我的作品集
              </h1>
              <p 
                style={{
                  fontSize: '1.2rem',
                  color: '#6b7280',
                  fontWeight: '400',
                  maxWidth: '600px',
                  margin: '0 auto'
                }}
              >
                探索我的創意作品和技術專案，每個項目都展現了我對前端開發的熱情與專業技能
              </p>
            </div>
          </Col>
        </Row>

        {/* Category Filter */}
        <Row className="mb-5">
          <Col>
            <div className="d-flex flex-wrap justify-content-center gap-3">
              {categories.map((category) => (
                <Button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  style={{
                    borderRadius: '25px',
                    padding: '8px 20px',
                    background: selectedCategory === category 
                      ? 'linear-gradient(145deg, #0891b2, #06b6d4)' 
                      : 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                    border: `1px solid ${selectedCategory === category ? '#0891b2' : '#d1d5db'}`,
                    color: selectedCategory === category ? 'white' : '#6b7280',
                    fontWeight: '500',
                    boxShadow: selectedCategory === category
                      ? '3px 3px 8px rgba(8, 145, 178, 0.3), inset 1px 1px 2px rgba(255,255,255,0.3)'
                      : '2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)',
                    transition: 'all 0.3s ease'
                  }}
                  className="category-filter-btn"
                >
                  {category}
                </Button>
              ))}
            </div>
          </Col>
        </Row>

        {/* Projects Grid */}
        <Row>
          {filteredProjects.map((project) => (
            <Col lg={4} md={6} key={project.id} className="mb-4">
              <Card 
                style={{
                  background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                  border: 'none',
                  borderRadius: '20px',
                  boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                  overflow: 'hidden',
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                className="project-card"
                onClick={() => handleProjectClick(project)}
              >
                {/* Project Image/Icon */}
                <div 
                  style={{
                    height: '200px',
                    background: 'linear-gradient(135deg, #0891b2, #f97316)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '4rem',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  {/* 裝飾性背景 */}
                  <div 
                    style={{
                      position: 'absolute',
                      top: '-20px',
                      right: '-20px',
                      width: '80px',
                      height: '80px',
                      borderRadius: '50%',
                      background: 'rgba(255,255,255,0.2)',
                      filter: 'blur(20px)'
                    }}
                  ></div>
                  {project.image}
                </div>

                <Card.Body style={{ padding: '2rem' }}>
                  <div className="d-flex justify-content-between align-items-start mb-3">
                    <Badge 
                      style={{
                        background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                        color: 'white',
                        padding: '6px 12px',
                        borderRadius: '15px',
                        fontSize: '0.8rem',
                        fontWeight: '500'
                      }}
                    >
                      {project.category}
                    </Badge>
                    <Badge 
                      style={{
                        background: getStatusColor(project.status),
                        color: 'white',
                        padding: '4px 8px',
                        borderRadius: '10px',
                        fontSize: '0.7rem'
                      }}
                    >
                      {project.status}
                    </Badge>
                  </div>

                  <h3 
                    style={{
                      fontSize: '1.3rem',
                      fontWeight: '600',
                      color: '#374151',
                      marginBottom: '1rem',
                      lineHeight: '1.4'
                    }}
                  >
                    {project.title}
                  </h3>

                  <p 
                    style={{
                      color: '#6b7280',
                      fontSize: '0.95rem',
                      lineHeight: '1.6',
                      marginBottom: '1.5rem'
                    }}
                  >
                    {project.description}
                  </p>

                  {/* Technologies */}
                  <div className="d-flex flex-wrap gap-2 mb-3">
                    {project.technologies.slice(0, 4).map((tech, index) => (
                      <div
                        key={index}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '4px',
                          padding: '4px 8px',
                          background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                          borderRadius: '8px',
                          fontSize: '0.8rem',
                          color: '#6b7280',
                          boxShadow: 'inset 1px 1px 2px rgba(0,0,0,0.05)'
                        }}
                      >
                        {getTechIcon(tech)}
                        <span>{tech}</span>
                      </div>
                    ))}
                  </div>

                  <div className="d-flex justify-content-between align-items-center">
                    <Button
                      variant="link"
                      style={{
                        color: '#0891b2',
                        fontWeight: '500',
                        textDecoration: 'none',
                        padding: '0'
                      }}
                    >
                      查看詳情 →
                    </Button>
                    <div className="d-flex gap-2">
                      <a
                        href={project.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          color: '#6b7280',
                          fontSize: '1.2rem',
                          transition: 'color 0.3s ease'
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <FaGithub />
                      </a>
                      <a
                        href={project.liveUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{
                          color: '#6b7280',
                          fontSize: '1.2rem',
                          transition: 'color 0.3s ease'
                        }}
                        onClick={(e) => e.stopPropagation()}
                      >
                        <FaExternalLinkAlt />
                      </a>
                    </div>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>
      </Container>

      {/* Project Detail Modal */}
      <Modal
        show={showModal}
        onHide={() => setShowModal(false)}
        size="lg"
        centered
      >
        <Modal.Header
          closeButton
          style={{
            background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
            border: 'none',
            borderRadius: '15px 15px 0 0'
          }}
        >
          <Modal.Title
            style={{
              color: '#0891b2',
              fontWeight: '600'
            }}
          >
            {selectedProject?.title}
          </Modal.Title>
        </Modal.Header>

        <Modal.Body
          style={{
            background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
            padding: '2rem'
          }}
        >
          {selectedProject && (
            <>
              <div className="text-center mb-4">
                <div
                  style={{
                    fontSize: '5rem',
                    background: 'linear-gradient(135deg, #0891b2, #f97316)',
                    borderRadius: '20px',
                    padding: '2rem',
                    display: 'inline-block',
                    boxShadow: '5px 5px 15px rgba(0,0,0,0.1)'
                  }}
                >
                  {selectedProject.image}
                </div>
              </div>

              <div className="mb-4">
                <h5 style={{ color: '#374151', marginBottom: '1rem' }}>專案描述</h5>
                <p style={{ color: '#6b7280', lineHeight: '1.6' }}>
                  {selectedProject.description}
                </p>
              </div>

              <div className="mb-4">
                <h5 style={{ color: '#374151', marginBottom: '1rem' }}>主要功能</h5>
                <Row>
                  {selectedProject.features?.map((feature, index) => (
                    <Col md={6} key={index} className="mb-2">
                      <div
                        style={{
                          padding: '8px 12px',
                          background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                          borderRadius: '8px',
                          color: '#6b7280',
                          fontSize: '0.9rem',
                          boxShadow: 'inset 1px 1px 2px rgba(0,0,0,0.05)'
                        }}
                      >
                        ✓ {feature}
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>

              <div className="mb-4">
                <h5 style={{ color: '#374151', marginBottom: '1rem' }}>使用技術</h5>
                <div className="d-flex flex-wrap gap-2">
                  {selectedProject.technologies?.map((tech, index) => (
                    <div
                      key={index}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '6px',
                        padding: '8px 12px',
                        background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                        borderRadius: '12px',
                        fontSize: '0.9rem',
                        color: 'white',
                        fontWeight: '500',
                        boxShadow: '3px 3px 8px rgba(8, 145, 178, 0.3)'
                      }}
                    >
                      {getTechIcon(tech)}
                      <span>{tech}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="d-flex gap-3 justify-content-center">
                <Button
                  href={selectedProject.githubUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '12px 24px',
                    background: 'linear-gradient(145deg, #374151, #1f2937)',
                    border: 'none',
                    borderRadius: '15px',
                    color: 'white',
                    fontWeight: '600',
                    boxShadow: '3px 3px 8px rgba(0,0,0,0.2)',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    textDecoration: 'none'
                  }}
                >
                  <FaGithub />
                  查看代碼
                </Button>

                <Button
                  href={selectedProject.liveUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    padding: '12px 24px',
                    background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                    border: 'none',
                    borderRadius: '15px',
                    color: 'white',
                    fontWeight: '600',
                    boxShadow: '3px 3px 8px rgba(8, 145, 178, 0.3)',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    textDecoration: 'none'
                  }}
                >
                  <FaExternalLinkAlt />
                  查看網站
                </Button>
              </div>
            </>
          )}
        </Modal.Body>
      </Modal>
    </div>
  )
}

export default PortfolioPage
