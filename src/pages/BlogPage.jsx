import Section from '../components/common/Section'

const BlogPage = () => {
  const blogPosts = [
    {
      id: 1,
      title: 'React 18 新功能深度解析',
      excerpt: '探索 React 18 帶來的新特性，包括 Concurrent Features、Automatic Batching 等重要更新...',
      date: '2023-12-01',
      category: 'React',
      readTime: '5 分鐘',
      image: '⚛️'
    },
    {
      id: 2,
      title: '現代 CSS 布局技巧',
      excerpt: '學習如何使用 CSS Grid 和 Flexbox 創建響應式布局，提升網頁設計的靈活性...',
      date: '2023-11-15',
      category: 'CSS',
      readTime: '8 分鐘',
      image: '🎨'
    },
    {
      id: 3,
      title: 'JavaScript 性能優化指南',
      excerpt: '深入了解 JavaScript 性能優化的最佳實踐，從代碼層面到運行時優化...',
      date: '2023-11-01',
      category: 'JavaScript',
      readTime: '10 分鐘',
      image: '⚡'
    },
    {
      id: 4,
      title: 'TypeScript 進階技巧',
      excerpt: '掌握 TypeScript 的高級特性，提升代碼的類型安全性和開發效率...',
      date: '2023-10-20',
      category: 'TypeScript',
      readTime: '7 分鐘',
      image: '📘'
    },
    {
      id: 5,
      title: 'Vite 建構工具完全指南',
      excerpt: '了解 Vite 的工作原理和配置技巧，打造高效的前端開發環境...',
      date: '2023-10-05',
      category: '工具',
      readTime: '6 分鐘',
      image: '🛠️'
    },
    {
      id: 6,
      title: '前端測試策略與實踐',
      excerpt: '建立完整的前端測試體系，包括單元測試、整合測試和端到端測試...',
      date: '2023-09-25',
      category: '測試',
      readTime: '12 分鐘',
      image: '🧪'
    }
  ]

  const categories = ['全部', 'React', 'JavaScript', 'CSS', 'TypeScript', '工具', '測試']

  return (
    <div className="pt-16 lg:pt-20">
      <Section 
        title="技術部落格" 
        subtitle="分享我的學習心得和技術見解"
        className="bg-gray-50 dark:bg-gray-900"
      >
        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              className="px-4 py-2 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-full border border-gray-300 dark:border-gray-600 hover:bg-primary-600 hover:text-white hover:border-primary-600 transition-colors"
            >
              {category}
            </button>
          ))}
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {blogPosts.map((post) => (
            <article 
              key={post.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
            >
              {/* Post Image/Icon */}
              <div className="h-48 bg-gradient-to-br from-primary-100 to-secondary-100 dark:from-gray-700 dark:to-gray-600 flex items-center justify-center">
                <span className="text-6xl">{post.image}</span>
              </div>

              {/* Post Content */}
              <div className="p-6">
                {/* Category & Read Time */}
                <div className="flex justify-between items-center mb-3">
                  <span className="px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-400 text-sm rounded-full">
                    {post.category}
                  </span>
                  <span className="text-gray-500 dark:text-gray-400 text-sm">
                    {post.readTime}
                  </span>
                </div>

                {/* Title */}
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3 line-clamp-2">
                  {post.title}
                </h3>

                {/* Excerpt */}
                <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed mb-4 line-clamp-3">
                  {post.excerpt}
                </p>

                {/* Date */}
                <div className="flex justify-between items-center">
                  <span className="text-gray-500 dark:text-gray-400 text-sm">
                    {new Date(post.date).toLocaleDateString('zh-TW')}
                  </span>
                  <button className="text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-sm font-medium">
                    閱讀更多 →
                  </button>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* Load More Button */}
        <div className="text-center mt-12">
          <button className="px-8 py-3 bg-primary-600 text-white font-medium rounded-lg hover:bg-primary-700 transition-colors">
            載入更多文章
          </button>
        </div>
      </Section>
    </div>
  )
}

export default BlogPage
