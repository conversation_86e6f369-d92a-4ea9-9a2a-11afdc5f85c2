import React from 'react'
import { Container, Row, Col, <PERSON>, But<PERSON>, Badge } from 'react-bootstrap'

const BlogPage = () => {
  const blogPosts = [
    {
      id: 1,
      title: 'React 18 新功能深度解析',
      excerpt: '探索 React 18 帶來的新特性，包括 Concurrent Features、Automatic Batching 等重要更新...',
      date: '2023-12-01',
      category: 'React',
      readTime: '5 分鐘',
      image: '⚛️'
    },
    {
      id: 2,
      title: '現代 CSS 布局技巧',
      excerpt: '學習如何使用 CSS Grid 和 Flexbox 創建響應式布局，提升網頁設計的靈活性...',
      date: '2023-11-15',
      category: 'CSS',
      readTime: '8 分鐘',
      image: '🎨'
    },
    {
      id: 3,
      title: 'JavaScript 性能優化指南',
      excerpt: '深入了解 JavaScript 性能優化的最佳實踐，從代碼層面到運行時優化...',
      date: '2023-11-01',
      category: 'JavaScript',
      readTime: '10 分鐘',
      image: '⚡'
    },
    {
      id: 4,
      title: 'TypeScript 進階技巧',
      excerpt: '掌握 TypeScript 的高級特性，提升代碼的類型安全性和開發效率...',
      date: '2023-10-20',
      category: 'TypeScript',
      readTime: '7 分鐘',
      image: '📘'
    },
    {
      id: 5,
      title: 'Vite 建構工具完全指南',
      excerpt: '了解 Vite 的工作原理和配置技巧，打造高效的前端開發環境...',
      date: '2023-10-05',
      category: '工具',
      readTime: '6 分鐘',
      image: '🛠️'
    },
    {
      id: 6,
      title: '前端測試策略與實踐',
      excerpt: '建立完整的前端測試體系，包括單元測試、整合測試和端到端測試...',
      date: '2023-09-25',
      category: '測試',
      readTime: '12 分鐘',
      image: '🧪'
    }
  ]

  const categories = ['全部', 'React', 'JavaScript', 'CSS', 'TypeScript', '工具', '測試']

  return (
    <div style={{ paddingTop: '120px', minHeight: '100vh' }}>
      <Container>
        {/* Hero Section */}
        <Row className="mb-5">
          <Col>
            <div className="text-center mb-5">
              <h1
                style={{
                  fontSize: '3rem',
                  fontWeight: '700',
                  color: '#0891b2',
                  textShadow: '2px 2px 4px rgba(255,255,255,0.8)',
                  marginBottom: '1rem'
                }}
              >
                技術部落格
              </h1>
              <p
                style={{
                  fontSize: '1.2rem',
                  color: '#6b7280',
                  fontWeight: '400'
                }}
              >
                分享我的學習心得和技術見解
              </p>
            </div>
          </Col>
        </Row>

        {/* Category Filter */}
        <Row className="mb-5">
          <Col>
            <div className="d-flex flex-wrap justify-content-center gap-3">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant="outline-primary"
                  style={{
                    borderRadius: '25px',
                    padding: '8px 20px',
                    background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                    border: '1px solid #0891b2',
                    color: '#0891b2',
                    fontWeight: '500',
                    boxShadow: '2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8)',
                    transition: 'all 0.3s ease'
                  }}
                  className="category-btn"
                >
                  {category}
                </Button>
              ))}
            </div>
          </Col>
        </Row>

        {/* Blog Posts Grid */}
        <Row>
          {blogPosts.map((post) => (
            <Col lg={4} md={6} key={post.id} className="mb-4">
              <Card
                style={{
                  background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                  border: 'none',
                  borderRadius: '20px',
                  boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                  overflow: 'hidden',
                  height: '100%',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                className="blog-card"
              >
                {/* Post Image/Icon */}
                <div
                  style={{
                    height: '200px',
                    background: 'linear-gradient(135deg, #0891b2, #f97316)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '4rem'
                  }}
                >
                  {post.image}
                </div>

                <Card.Body style={{ padding: '2rem' }}>
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <Badge
                      style={{
                        background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                        color: 'white',
                        padding: '6px 12px',
                        borderRadius: '15px',
                        fontSize: '0.8rem',
                        fontWeight: '500'
                      }}
                    >
                      {post.category}
                    </Badge>
                    <span
                      style={{
                        color: '#6b7280',
                        fontSize: '0.9rem'
                      }}
                    >
                      {post.readTime}
                    </span>
                  </div>

                  <h3
                    style={{
                      fontSize: '1.3rem',
                      fontWeight: '600',
                      color: '#374151',
                      marginBottom: '1rem',
                      lineHeight: '1.4'
                    }}
                  >
                    {post.title}
                  </h3>

                  <p
                    style={{
                      color: '#6b7280',
                      fontSize: '0.95rem',
                      lineHeight: '1.6',
                      marginBottom: '1.5rem'
                    }}
                  >
                    {post.excerpt}
                  </p>

                  <div className="d-flex justify-content-between align-items-center">
                    <span
                      style={{
                        color: '#9ca3af',
                        fontSize: '0.9rem'
                      }}
                    >
                      {new Date(post.date).toLocaleDateString('zh-TW')}
                    </span>
                    <Button
                      variant="link"
                      style={{
                        color: '#0891b2',
                        fontWeight: '500',
                        textDecoration: 'none',
                        padding: '0'
                      }}
                    >
                      閱讀更多 →
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </Col>
          ))}
        </Row>

        {/* Load More Button */}
        <Row className="mt-5">
          <Col className="text-center">
            <Button
              style={{
                padding: '12px 30px',
                background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                border: 'none',
                borderRadius: '25px',
                color: 'white',
                fontWeight: '600',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.2), inset 1px 1px 3px rgba(255,255,255,0.3)',
                transition: 'all 0.3s ease'
              }}
            >
              載入更多文章
            </Button>
          </Col>
        </Row>
      </Container>
    </div>
  )
}

export default BlogPage
