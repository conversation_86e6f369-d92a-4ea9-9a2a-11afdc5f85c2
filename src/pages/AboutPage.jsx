import Section from '../components/common/Section'

const AboutPage = () => {
  return (
    <div className="pt-16 lg:pt-20">
      <Section 
        title="關於我" 
        subtitle="了解我的背景、經歷和熱情"
        className="bg-gray-50 dark:bg-gray-900"
      >
        <div className="max-w-4xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Profile Image */}
            <div className="text-center lg:text-left">
              <div className="w-64 h-64 mx-auto lg:mx-0 bg-gray-300 dark:bg-gray-700 rounded-full mb-6 flex items-center justify-center">
                <span className="text-6xl">👨‍💻</span>
              </div>
            </div>

            {/* About Content */}
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                Hello! 我是 Chan Chun Yin
              </h3>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                我是一位充滿熱情的前端工程師，專注於創造優質的用戶體驗和現代化的網頁應用程式。
                擁有豐富的 React、JavaScript 和現代前端技術經驗。
              </p>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                我相信技術應該服務於人，致力於將複雜的概念轉化為簡潔、直觀的用戶界面。
                持續學習新技術，保持對創新的敏感度。
              </p>
            </div>
          </div>
        </div>
      </Section>

      {/* Experience Timeline */}
      <Section title="經歷">
        <div className="max-w-3xl mx-auto">
          <div className="space-y-8">
            <div className="border-l-4 border-primary-600 pl-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  前端工程師
                </h4>
                <p className="text-primary-600 dark:text-primary-400 mb-2">2023 - 現在</p>
                <p className="text-gray-600 dark:text-gray-300">
                  負責開發和維護多個 React 專案，優化用戶體驗和網站性能。
                </p>
              </div>
            </div>

            <div className="border-l-4 border-gray-300 pl-6">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                <h4 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  初級前端開發者
                </h4>
                <p className="text-gray-500 dark:text-gray-400 mb-2">2022 - 2023</p>
                <p className="text-gray-600 dark:text-gray-300">
                  學習現代前端技術，參與團隊專案開發，建立扎實的技術基礎。
                </p>
              </div>
            </div>
          </div>
        </div>
      </Section>
    </div>
  )
}

export default AboutPage
