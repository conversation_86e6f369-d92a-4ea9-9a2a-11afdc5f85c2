import React from 'react'
import { Container, Row, Col, Card } from 'react-bootstrap'

const AboutPage = () => {
  return (
    <div style={{ paddingTop: '120px', minHeight: '100vh' }}>
      <Container>
        {/* Hero Section */}
        <Row className="mb-5">
          <Col>
            <div className="text-center mb-5">
              <h1
                style={{
                  fontSize: '3rem',
                  fontWeight: '700',
                  color: '#0891b2',
                  textShadow: '2px 2px 4px rgba(255,255,255,0.8)',
                  marginBottom: '1rem'
                }}
              >
                關於我
              </h1>
              <p
                style={{
                  fontSize: '1.2rem',
                  color: '#6b7280',
                  fontWeight: '400'
                }}
              >
                了解我的背景、經歷和熱情
              </p>
            </div>
          </Col>
        </Row>

        {/* Main Content */}
        <Row className="align-items-center mb-5">
          {/* Profile Image */}
          <Col lg={5} className="text-center mb-4 mb-lg-0">
            <div
              style={{
                width: '280px',
                height: '280px',
                margin: '0 auto',
                borderRadius: '50%',
                background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                boxShadow: '10px 10px 20px rgba(0,0,0,0.1), inset 2px 2px 5px rgba(255,255,255,0.8)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '6rem'
              }}
            >
              👨‍💻
            </div>
          </Col>

          {/* About Content */}
          <Col lg={7}>
            <Card
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                border: 'none',
                borderRadius: '20px',
                boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                padding: '2rem'
              }}
            >
              <Card.Body>
                <h3
                  style={{
                    fontSize: '2rem',
                    fontWeight: '600',
                    color: '#0891b2',
                    marginBottom: '1.5rem',
                    textShadow: '1px 1px 2px rgba(255,255,255,0.8)'
                  }}
                >
                  Hello! 我是 Chan Chun Yin
                </h3>
                <p
                  style={{
                    color: '#4b5563',
                    lineHeight: '1.7',
                    marginBottom: '1.5rem',
                    fontSize: '1.1rem'
                  }}
                >
                  我是一位充滿熱情的前端工程師，專注於創造優質的用戶體驗和現代化的網頁應用程式。
                  擁有豐富的 React、JavaScript 和現代前端技術經驗。
                </p>
                <p
                  style={{
                    color: '#4b5563',
                    lineHeight: '1.7',
                    fontSize: '1.1rem'
                  }}
                >
                  我相信技術應該服務於人，致力於將複雜的概念轉化為簡潔、直觀的用戶界面。
                  持續學習新技術，保持對創新的敏感度。
                </p>
              </Card.Body>
            </Card>
          </Col>
        </Row>

        {/* Experience Timeline */}
        <Row>
          <Col>
            <div className="text-center mb-5">
              <h2
                style={{
                  fontSize: '2.5rem',
                  fontWeight: '600',
                  color: '#0891b2',
                  textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                  marginBottom: '3rem'
                }}
              >
                經歷
              </h2>
            </div>
          </Col>
        </Row>

        <Row className="justify-content-center">
          <Col lg={8}>
            <div className="position-relative">
              {/* Timeline Line */}
              <div
                style={{
                  position: 'absolute',
                  left: '30px',
                  top: '0',
                  bottom: '0',
                  width: '4px',
                  background: 'linear-gradient(to bottom, #0891b2, #f97316)',
                  borderRadius: '2px'
                }}
              ></div>

              {/* Experience Items */}
              <div className="mb-4">
                <Card
                  style={{
                    background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                    border: 'none',
                    borderRadius: '15px',
                    boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                    marginLeft: '60px',
                    position: 'relative'
                  }}
                >
                  {/* Timeline Dot */}
                  <div
                    style={{
                      position: 'absolute',
                      left: '-45px',
                      top: '20px',
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      background: '#0891b2',
                      boxShadow: '0 0 0 4px rgba(255,255,255,1), 0 0 0 8px rgba(8, 145, 178, 0.2)'
                    }}
                  ></div>

                  <Card.Body style={{ padding: '2rem' }}>
                    <h4
                      style={{
                        fontSize: '1.5rem',
                        fontWeight: '600',
                        color: '#0891b2',
                        marginBottom: '0.5rem'
                      }}
                    >
                      前端工程師
                    </h4>
                    <p
                      style={{
                        color: '#f97316',
                        fontWeight: '500',
                        marginBottom: '1rem'
                      }}
                    >
                      2023 - 現在
                    </p>
                    <p
                      style={{
                        color: '#4b5563',
                        lineHeight: '1.6'
                      }}
                    >
                      負責開發和維護多個 React 專案，優化用戶體驗和網站性能。
                    </p>
                  </Card.Body>
                </Card>
              </div>

              <div className="mb-4">
                <Card
                  style={{
                    background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                    border: 'none',
                    borderRadius: '15px',
                    boxShadow: '5px 5px 15px rgba(0,0,0,0.1), inset 1px 1px 3px rgba(255,255,255,0.8)',
                    marginLeft: '60px',
                    position: 'relative'
                  }}
                >
                  {/* Timeline Dot */}
                  <div
                    style={{
                      position: 'absolute',
                      left: '-45px',
                      top: '20px',
                      width: '20px',
                      height: '20px',
                      borderRadius: '50%',
                      background: '#6b7280',
                      boxShadow: '0 0 0 4px rgba(255,255,255,1), 0 0 0 8px rgba(107, 114, 128, 0.2)'
                    }}
                  ></div>

                  <Card.Body style={{ padding: '2rem' }}>
                    <h4
                      style={{
                        fontSize: '1.5rem',
                        fontWeight: '600',
                        color: '#6b7280',
                        marginBottom: '0.5rem'
                      }}
                    >
                      初級前端開發者
                    </h4>
                    <p
                      style={{
                        color: '#9ca3af',
                        fontWeight: '500',
                        marginBottom: '1rem'
                      }}
                    >
                      2022 - 2023
                    </p>
                    <p
                      style={{
                        color: '#4b5563',
                        lineHeight: '1.6'
                      }}
                    >
                      學習現代前端技術，參與團隊專案開發，建立扎實的技術基礎。
                    </p>
                  </Card.Body>
                </Card>
              </div>
            </div>
          </Col>
        </Row>

        {/* Education Section */}
        <Row className="mt-5">
          <Col>
            <div className="text-center mb-5">
              <h2
                style={{
                  fontSize: '2.5rem',
                  fontWeight: '600',
                  color: '#0891b2',
                  textShadow: '1px 1px 2px rgba(255,255,255,0.8)',
                  marginBottom: '3rem'
                }}
              >
                教育背景
              </h2>
            </div>
          </Col>
        </Row>

        <Row className="justify-content-center mb-5">
          <Col lg={8}>
            <Card
              style={{
                background: 'linear-gradient(145deg, #ffffff, #f1f5f9)',
                border: 'none',
                borderRadius: '20px',
                boxShadow: '8px 8px 25px rgba(0,0,0,0.1), inset 2px 2px 5px rgba(255,255,255,0.8)',
                overflow: 'hidden',
                transition: 'all 0.3s ease'
              }}
              className="education-card"
            >
              {/* University Header */}
              <div
                style={{
                  background: 'linear-gradient(135deg, #0891b2, #06b6d4)',
                  padding: '2rem',
                  color: 'white',
                  position: 'relative'
                }}
                className="education-header"
              >
                {/* Decorative Elements */}
                <div
                  style={{
                    position: 'absolute',
                    top: '-20px',
                    right: '-20px',
                    width: '100px',
                    height: '100px',
                    borderRadius: '50%',
                    background: 'rgba(255,255,255,0.1)',
                    filter: 'blur(30px)'
                  }}
                ></div>
                <div
                  style={{
                    position: 'absolute',
                    bottom: '-10px',
                    left: '-10px',
                    width: '60px',
                    height: '60px',
                    borderRadius: '50%',
                    background: 'rgba(255,255,255,0.1)',
                    filter: 'blur(20px)'
                  }}
                ></div>

                <div style={{ position: 'relative', zIndex: 2 }}>
                  <div className="d-flex align-items-center mb-3">
                    <div
                      style={{
                        width: '60px',
                        height: '60px',
                        borderRadius: '15px',
                        background: 'linear-gradient(145deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1))',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '2rem',
                        marginRight: '1rem',
                        boxShadow: '3px 3px 10px rgba(0,0,0,0.2), inset 1px 1px 2px rgba(255,255,255,0.3)'
                      }}
                    >
                      🎓
                    </div>
                    <div>
                      <h3
                        style={{
                          fontSize: '1.8rem',
                          fontWeight: '700',
                          margin: '0',
                          textShadow: '1px 1px 2px rgba(0,0,0,0.2)'
                        }}
                      >
                        University of the West of England
                      </h3>
                      <p
                        style={{
                          fontSize: '1.1rem',
                          margin: '0',
                          opacity: '0.9'
                        }}
                      >
                        Bristol, United Kingdom
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Degree Details */}
              <Card.Body style={{ padding: '2.5rem' }}>
                <Row>
                  <Col md={8}>
                    <h4
                      style={{
                        fontSize: '1.5rem',
                        fontWeight: '600',
                        color: '#374151',
                        marginBottom: '1rem'
                      }}
                    >
                      Electronic and Computer Engineering
                    </h4>
                    <div className="mb-3">
                      <span
                        style={{
                          display: 'inline-block',
                          padding: '6px 16px',
                          background: 'linear-gradient(145deg, #0891b2, #06b6d4)',
                          color: 'white',
                          borderRadius: '20px',
                          fontSize: '0.9rem',
                          fontWeight: '500',
                          boxShadow: '3px 3px 8px rgba(8, 145, 178, 0.3)'
                        }}
                        className="degree-badge"
                      >
                        學士學位 Bachelor's Degree
                      </span>
                    </div>
                    <p
                      style={{
                        color: '#6b7280',
                        lineHeight: '1.7',
                        fontSize: '1rem'
                      }}
                    >
                      主修電子與計算機工程，專注於軟體開發、電路設計和系統整合。
                      在學期間深入學習程式設計、數位系統設計、嵌入式系統開發等核心技術，
                      為後續的前端開發和 AI 應用奠定了堅實的技術基礎。
                    </p>
                  </Col>
                  <Col md={4} className="text-center">
                    <div
                      style={{
                        padding: '1.5rem',
                        background: 'linear-gradient(145deg, #f8fafc, #e2e8f0)',
                        borderRadius: '15px',
                        boxShadow: 'inset 2px 2px 5px rgba(0,0,0,0.05)'
                      }}
                    >
                      <div
                        style={{
                          fontSize: '3rem',
                          marginBottom: '1rem'
                        }}
                      >
                        🏛️
                      </div>
                      <h5
                        style={{
                          color: '#0891b2',
                          fontWeight: '600',
                          marginBottom: '0.5rem'
                        }}
                      >
                        工程學院
                      </h5>
                      <p
                        style={{
                          color: '#6b7280',
                          fontSize: '0.9rem',
                          margin: '0'
                        }}
                      >
                        Faculty of Engineering
                      </p>
                    </div>
                  </Col>
                </Row>

                {/* Key Subjects */}
                <div className="mt-4">
                  <h5
                    style={{
                      color: '#374151',
                      fontWeight: '600',
                      marginBottom: '1rem'
                    }}
                  >
                    主要課程
                  </h5>
                  <Row>
                    <Col md={6}>
                      <ul style={{ listStyle: 'none', padding: '0' }}>
                        <li
                          style={{
                            padding: '8px 12px',
                            borderBottom: '1px solid rgba(8, 145, 178, 0.1)',
                            color: '#4b5563',
                            borderRadius: '8px',
                            margin: '4px 0',
                            transition: 'all 0.3s ease'
                          }}
                          className="subject-item"
                        >
                          📱 軟體工程與程式設計
                        </li>
                        <li
                          style={{
                            padding: '8px 0',
                            borderBottom: '1px solid rgba(8, 145, 178, 0.1)',
                            color: '#4b5563'
                          }}
                        >
                          🔌 數位電路設計
                        </li>
                        <li
                          style={{
                            padding: '8px 0',
                            borderBottom: '1px solid rgba(8, 145, 178, 0.1)',
                            color: '#4b5563'
                          }}
                        >
                          🖥️ 計算機系統架構
                        </li>
                      </ul>
                    </Col>
                    <Col md={6}>
                      <ul style={{ listStyle: 'none', padding: '0' }}>
                        <li
                          style={{
                            padding: '8px 0',
                            borderBottom: '1px solid rgba(8, 145, 178, 0.1)',
                            color: '#4b5563'
                          }}
                        >
                          🤖 嵌入式系統開發
                        </li>
                        <li
                          style={{
                            padding: '8px 0',
                            borderBottom: '1px solid rgba(8, 145, 178, 0.1)',
                            color: '#4b5563'
                          }}
                        >
                          📊 數據結構與演算法
                        </li>
                        <li
                          style={{
                            padding: '8px 0',
                            borderBottom: '1px solid rgba(8, 145, 178, 0.1)',
                            color: '#4b5563'
                          }}
                        >
                          🌐 網路通訊協定
                        </li>
                      </ul>
                    </Col>
                  </Row>
                </div>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      </Container>
    </div>
  )
}

export default AboutPage
