/* Import CSS Variables */
@import './variables.css';

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-primary);
  line-height: var(--leading-normal);
  color: var(--color-gray-800);
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #b3e5fc 100%);
  min-height: 100vh;
  transition: all 0.3s ease;
  font-weight: 400;
}

/* Dark mode */
html.dark body {
  color: var(--color-gray-100);
  background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
}

/* Container */
.container {
  width: 100%;
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 640px) {
  .container {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-heading);
  font-weight: var(--font-bold);
  line-height: var(--leading-tight);
  color: var(--color-gray-800);
  text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
}

html.dark h1,
html.dark h2,
html.dark h3,
html.dark h4,
html.dark h5,
html.dark h6 {
  color: var(--color-white);
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

@media (min-width: 768px) {
  h1 { font-size: var(--text-5xl); }
  h2 { font-size: var(--text-4xl); }
  h3 { font-size: var(--text-3xl); }
}

@media (min-width: 1024px) {
  h1 { font-size: var(--text-6xl); }
  h2 { font-size: var(--text-5xl); }
  h3 { font-size: var(--text-4xl); }
}

p {
  margin-bottom: var(--space-4);
  color: var(--color-gray-600);
}

html.dark p {
  color: var(--color-gray-300);
}

/* Links */
a {
  color: var(--color-primary-600);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--color-primary-700);
}

html.dark a {
  color: var(--color-primary-400);
}

html.dark a:hover {
  color: var(--color-primary-300);
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* Selection */
::selection {
  background-color: var(--color-primary-100);
  color: var(--color-primary-900);
}

html.dark ::selection {
  background-color: var(--color-primary-900);
  color: var(--color-primary-100);
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray-400);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}

html.dark ::-webkit-scrollbar-track {
  background: var(--color-gray-800);
}

html.dark ::-webkit-scrollbar-thumb {
  background: var(--color-gray-600);
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-500);
}

/* Modern Animations */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes grid-move {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(8, 145, 178, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(8, 145, 178, 0.8);
  }
}

@keyframes text-glow {
  0%, 100% {
    text-shadow: 0 0 10px rgba(8, 145, 178, 0.5);
  }
  50% {
    text-shadow: 0 0 20px rgba(8, 145, 178, 0.8);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animate-wave {
  animation: wave 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Loading states */
.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Smooth transitions */
.transition-all {
  transition: all 0.2s ease;
}

.transition-colors {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

.transition-transform {
  transition: transform 0.2s ease;
}

.transition-shadow {
  transition: box-shadow 0.2s ease;
}

/* Hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-scale:hover {
  transform: scale(1.05);
}

/* Print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a, a:visited {
    text-decoration: underline;
  }
  
  .no-print {
    display: none !important;
  }
}

/* Bootstrap 和 Web 2.0 樣式增強 */
.nav-link-custom:hover {
  background: linear-gradient(145deg, #e2e8f0, #cbd5e1) !important;
  box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1), inset -1px -1px 2px rgba(255,255,255,0.8) !important;
  transform: translateY(1px);
  color: #0891b2 !important;
}

.custom-navbar .navbar-toggler {
  border: none;
  background: linear-gradient(145deg, #f8fafc, #e2e8f0);
  box-shadow: 2px 2px 5px rgba(0,0,0,0.1), inset 1px 1px 2px rgba(255,255,255,0.8);
  border-radius: 8px;
}

.custom-navbar .navbar-toggler:focus {
  box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1), inset -1px -1px 2px rgba(255,255,255,0.8);
}

/* 主題切換按鈕效果 */
button[aria-label="切換主題"]:hover {
  background: linear-gradient(145deg, #e2e8f0, #cbd5e1) !important;
  box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1), inset -1px -1px 2px rgba(255,255,255,0.8) !important;
  transform: translateY(1px);
}

/* 響應式調整 */
@media (max-width: 991.98px) {
  .custom-navbar .navbar-collapse {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    margin-top: 10px;
    padding: 20px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
  }

  .nav-link-custom {
    margin: 5px 0 !important;
    text-align: center;
  }
}

/* 頁面特定樣式 */
.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 8px 8px 25px rgba(0,0,0,0.15), inset 1px 1px 3px rgba(255,255,255,0.8) !important;
}

.category-btn:hover {
  background: linear-gradient(145deg, #e2e8f0, #cbd5e1) !important;
  box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1), inset -1px -1px 2px rgba(255,255,255,0.8) !important;
  transform: translateY(1px);
  color: #0891b2 !important;
}

/* 表單樣式增強 */
.form-control:focus {
  border-color: #0891b2 !important;
  box-shadow: 0 0 0 0.2rem rgba(8, 145, 178, 0.25) !important;
  background: linear-gradient(145deg, #ffffff, #f8fafc) !important;
}

/* 社交媒體按鈕效果 */
a[aria-label]:hover {
  background: linear-gradient(145deg, #e2e8f0, #cbd5e1) !important;
  box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1), inset -1px -1px 2px rgba(255,255,255,0.8) !important;
  transform: translateY(1px) scale(1.05);
  color: #0891b2 !important;
}

/* 按鈕懸停效果 */
.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;
}

/* 卡片懸停效果 */
.card:hover {
  transform: translateY(-3px);
  box-shadow: 8px 8px 25px rgba(0,0,0,0.15), inset 1px 1px 3px rgba(255,255,255,0.8) !important;
}

/* 脈衝動畫 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Footer 特殊樣式 */
.social-link:hover {
  background: linear-gradient(145deg, #e2e8f0, #cbd5e1) !important;
  box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1), inset -1px -1px 2px rgba(255,255,255,0.8) !important;
  transform: translateY(1px) scale(1.05);
  color: #0891b2 !important;
}

.footer-link:hover {
  background: linear-gradient(145deg, #e2e8f0, #cbd5e1) !important;
  box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1), inset -1px -1px 2px rgba(255,255,255,0.8) !important;
  transform: translateY(1px);
  color: #0891b2 !important;
}

.back-to-top-btn:hover {
  background: linear-gradient(145deg, #e2e8f0, #cbd5e1) !important;
  box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1), inset -1px -1px 2px rgba(255,255,255,0.8) !important;
  transform: translateY(1px);
  color: #0891b2 !important;
  border-color: #0891b2 !important;
}

/* Footer 響應式調整 */
@media (max-width: 767.98px) {
  footer .d-flex {
    flex-direction: column !important;
    align-items: center !important;
    text-align: center;
  }

  footer .social-link {
    margin: 0 0.25rem !important;
  }
}

/* HomePage 特殊樣式 */
.skill-card:hover {
  transform: translateY(-8px);
  box-shadow: 10px 10px 30px rgba(0,0,0,0.15), inset 1px 1px 3px rgba(255,255,255,0.8) !important;
}

.skill-icon:hover {
  transform: scale(1.1);
  box-shadow: 8px 8px 20px rgba(8, 145, 178, 0.4), inset 1px 1px 3px rgba(255,255,255,0.3) !important;
}

/* AI 技能卡片特殊效果 */
.skill-card:nth-child(4) .skill-icon {
  background: linear-gradient(145deg, #8b5cf6, #7c3aed) !important;
  box-shadow: 5px 5px 15px rgba(139, 92, 246, 0.3), inset 1px 1px 3px rgba(255,255,255,0.3) !important;
}

.skill-card:nth-child(4) .skill-icon:hover {
  box-shadow: 8px 8px 20px rgba(139, 92, 246, 0.4), inset 1px 1px 3px rgba(255,255,255,0.3) !important;
  animation: ai-glow 2s ease-in-out infinite alternate;
}

/* AI 發光動畫 */
@keyframes ai-glow {
  0% {
    box-shadow: 8px 8px 20px rgba(139, 92, 246, 0.4), inset 1px 1px 3px rgba(255,255,255,0.3), 0 0 20px rgba(139, 92, 246, 0.3);
  }
  100% {
    box-shadow: 8px 8px 20px rgba(139, 92, 246, 0.6), inset 1px 1px 3px rgba(255,255,255,0.3), 0 0 30px rgba(139, 92, 246, 0.5);
  }
}

/* About 頁面教育背景特殊樣式 */
.education-card:hover {
  transform: translateY(-5px);
  box-shadow: 12px 12px 35px rgba(0,0,0,0.15), inset 2px 2px 5px rgba(255,255,255,0.8) !important;
}

.education-header {
  background: linear-gradient(135deg, #0891b2, #06b6d4) !important;
  transition: all 0.3s ease;
}

.education-card:hover .education-header {
  background: linear-gradient(135deg, #06b6d4, #0891b2) !important;
}

.subject-item:hover {
  background: linear-gradient(145deg, #f0f9ff, #e0f2fe) !important;
  color: #0891b2 !important;
  transform: translateX(5px);
  transition: all 0.3s ease;
}

/* 學位徽章動畫 */
.degree-badge {
  transition: all 0.3s ease;
}

.education-card:hover .degree-badge {
  transform: scale(1.05);
  box-shadow: 5px 5px 15px rgba(8, 145, 178, 0.4) !important;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 8px 8px 25px rgba(8, 145, 178, 0.3), inset 1px 1px 3px rgba(255,255,255,0.3) !important;
}

.contact-cta-button:hover {
  background: linear-gradient(145deg, #f1f5f9, #e2e8f0) !important;
  box-shadow: inset 3px 3px 8px rgba(0,0,0,0.1), inset -1px -1px 3px rgba(255,255,255,0.8) !important;
  transform: translateY(2px);
}

.hero-cta-primary:hover {
  transform: translateY(-3px);
  box-shadow: 8px 8px 25px rgba(8, 145, 178, 0.5), inset 1px 1px 3px rgba(255,255,255,0.3) !important;
}

.hero-cta-secondary:hover {
  background: linear-gradient(145deg, rgba(248,250,252,0.9), rgba(226,232,240,0.9)) !important;
  box-shadow: inset 3px 3px 8px rgba(8, 145, 178, 0.1), inset -1px -1px 3px rgba(255,255,255,0.8) !important;
  transform: translateY(2px);
}

/* 彈跳動畫 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0,-8px,0);
  }
  70% {
    transform: translate3d(0,-4px,0);
  }
  90% {
    transform: translate3d(0,-2px,0);
  }
}

/* Portfolio 頁面特殊樣式 */
.project-card:hover {
  transform: translateY(-8px);
  box-shadow: 10px 10px 30px rgba(0,0,0,0.15), inset 1px 1px 3px rgba(255,255,255,0.8) !important;
}

.category-filter-btn:hover {
  transform: translateY(-2px);
  box-shadow: 5px 5px 15px rgba(0,0,0,0.2) !important;
}

/* Modal 樣式增強 */
.modal-content {
  border: none !important;
  border-radius: 20px !important;
  box-shadow: 10px 10px 30px rgba(0,0,0,0.2) !important;
}

.modal-header {
  border-bottom: 1px solid rgba(8, 145, 178, 0.1) !important;
}

.modal-header .btn-close {
  background: linear-gradient(145deg, #f8fafc, #e2e8f0) !important;
  border-radius: 50% !important;
  opacity: 1 !important;
  box-shadow: 2px 2px 5px rgba(0,0,0,0.1) !important;
}

.modal-header .btn-close:hover {
  background: linear-gradient(145deg, #e2e8f0, #cbd5e1) !important;
  box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1) !important;
  transform: scale(0.95);
}

/* 項目狀態徽章動畫 */
.project-card .badge {
  transition: all 0.3s ease;
}

.project-card:hover .badge {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* 技術圖標懸停效果 */
.project-card a:hover {
  color: #0891b2 !important;
  transform: scale(1.2);
}

/* 響應式調整 */
@media (max-width: 767.98px) {
  .project-card {
    margin-bottom: 2rem;
  }

  .category-filter-btn {
    margin: 0.25rem !important;
    font-size: 0.9rem !important;
  }
}

/* Contact 頁面特殊樣式 */
.form-control:focus {
  border-color: #0891b2 !important;
  box-shadow: 0 0 0 0.2rem rgba(8, 145, 178, 0.25) !important;
  background: linear-gradient(145deg, #ffffff, #f8fafc) !important;
}

.form-control:hover {
  border-color: #06b6d4 !important;
  box-shadow: 2px 2px 8px rgba(0,0,0,0.1) !important;
}

/* Contact 卡片懸停效果 */
.contact-card:hover {
  transform: translateY(-3px);
  box-shadow: 8px 8px 25px rgba(0,0,0,0.15), inset 1px 1px 3px rgba(255,255,255,0.8) !important;
}

/* 社交媒體按鈕懸停效果 */
.social-btn:hover {
  background: linear-gradient(145deg, #e2e8f0, #cbd5e1) !important;
  box-shadow: inset 2px 2px 5px rgba(0,0,0,0.1), inset -1px -1px 2px rgba(255,255,255,0.8) !important;
  transform: translateY(1px) scale(1.05);
  color: #0891b2 !important;
}

/* 提交按鈕懸停效果 */
.submit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 8px 8px 25px rgba(8, 145, 178, 0.4), inset 1px 1px 3px rgba(255,255,255,0.3) !important;
}
