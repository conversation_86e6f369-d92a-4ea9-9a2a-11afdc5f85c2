{"version": 3, "sources": ["../../react-icons/lib/esm/iconBase.js", "../../react-icons/lib/esm/iconContext.js"], "sourcesContent": ["var __assign = this && this.__assign || function () {\n  __assign = Object.assign || function (t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nvar __rest = this && this.__rest || function (s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n};\nimport React from \"react\";\nimport { IconContext, DefaultContext } from \"./iconContext\";\nfunction Tree2Element(tree) {\n  return tree && tree.map(function (node, i) {\n    return React.createElement(node.tag, __assign({\n      key: i\n    }, node.attr), Tree2Element(node.child));\n  });\n}\nexport function GenIcon(data) {\n  // eslint-disable-next-line react/display-name\n  return function (props) {\n    return React.createElement(IconBase, __assign({\n      attr: __assign({}, data.attr)\n    }, props), Tree2Element(data.child));\n  };\n}\nexport function IconBase(props) {\n  var elem = function (conf) {\n    var attr = props.attr,\n      size = props.size,\n      title = props.title,\n      svgProps = __rest(props, [\"attr\", \"size\", \"title\"]);\n    var computedSize = size || conf.size || \"1em\";\n    var className;\n    if (conf.className) className = conf.className;\n    if (props.className) className = (className ? className + \" \" : \"\") + props.className;\n    return React.createElement(\"svg\", __assign({\n      stroke: \"currentColor\",\n      fill: \"currentColor\",\n      strokeWidth: \"0\"\n    }, conf.attr, attr, svgProps, {\n      className: className,\n      style: __assign(__assign({\n        color: props.color || conf.color\n      }, conf.style), props.style),\n      height: computedSize,\n      width: computedSize,\n      xmlns: \"http://www.w3.org/2000/svg\"\n    }), title && React.createElement(\"title\", null, title), props.children);\n  };\n  return IconContext !== undefined ? React.createElement(IconContext.Consumer, null, function (conf) {\n    return elem(conf);\n  }) : elem(DefaultContext);\n}", "import React from \"react\";\nexport var DefaultContext = {\n  color: undefined,\n  size: undefined,\n  className: undefined,\n  style: undefined,\n  attr: undefined\n};\nexport var IconContext = React.createContext && React.createContext(DefaultContext);"], "mappings": ";;;;;;;;AAkBA,IAAAA,gBAAkB;;;AClBlB,mBAAkB;AACX,IAAI,iBAAiB;AAAA,EAC1B,OAAO;AAAA,EACP,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AACR;AACO,IAAI,cAAc,aAAAC,QAAM,iBAAiB,aAAAA,QAAM,cAAc,cAAc;;;ADRlF,IAAI,WAAoC,WAAY;AAClD,aAAW,OAAO,UAAU,SAAU,GAAG;AACvC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACnD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC7E;AACA,WAAO;AAAA,EACT;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;AACA,IAAI,SAAgC,SAAU,GAAG,GAAG;AAClD,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAC/F,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B,WAAY,UAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC3I,QAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC,EAAG,GAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,EAClG;AACA,SAAO;AACT;AAGA,SAAS,aAAa,MAAM;AAC1B,SAAO,QAAQ,KAAK,IAAI,SAAU,MAAM,GAAG;AACzC,WAAO,cAAAC,QAAM,cAAc,KAAK,KAAK,SAAS;AAAA,MAC5C,KAAK;AAAA,IACP,GAAG,KAAK,IAAI,GAAG,aAAa,KAAK,KAAK,CAAC;AAAA,EACzC,CAAC;AACH;AACO,SAAS,QAAQ,MAAM;AAE5B,SAAO,SAAU,OAAO;AACtB,WAAO,cAAAA,QAAM,cAAc,UAAU,SAAS;AAAA,MAC5C,MAAM,SAAS,CAAC,GAAG,KAAK,IAAI;AAAA,IAC9B,GAAG,KAAK,GAAG,aAAa,KAAK,KAAK,CAAC;AAAA,EACrC;AACF;AACO,SAAS,SAAS,OAAO;AAC9B,MAAI,OAAO,SAAU,MAAM;AACzB,QAAI,OAAO,MAAM,MACf,OAAO,MAAM,MACb,QAAQ,MAAM,OACd,WAAW,OAAO,OAAO,CAAC,QAAQ,QAAQ,OAAO,CAAC;AACpD,QAAI,eAAe,QAAQ,KAAK,QAAQ;AACxC,QAAI;AACJ,QAAI,KAAK,UAAW,aAAY,KAAK;AACrC,QAAI,MAAM,UAAW,cAAa,YAAY,YAAY,MAAM,MAAM,MAAM;AAC5E,WAAO,cAAAA,QAAM,cAAc,OAAO,SAAS;AAAA,MACzC,QAAQ;AAAA,MACR,MAAM;AAAA,MACN,aAAa;AAAA,IACf,GAAG,KAAK,MAAM,MAAM,UAAU;AAAA,MAC5B;AAAA,MACA,OAAO,SAAS,SAAS;AAAA,QACvB,OAAO,MAAM,SAAS,KAAK;AAAA,MAC7B,GAAG,KAAK,KAAK,GAAG,MAAM,KAAK;AAAA,MAC3B,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,IACT,CAAC,GAAG,SAAS,cAAAA,QAAM,cAAc,SAAS,MAAM,KAAK,GAAG,MAAM,QAAQ;AAAA,EACxE;AACA,SAAO,gBAAgB,SAAY,cAAAA,QAAM,cAAc,YAAY,UAAU,MAAM,SAAU,MAAM;AACjG,WAAO,KAAK,IAAI;AAAA,EAClB,CAAC,IAAI,KAAK,cAAc;AAC1B;", "names": ["import_react", "React", "React"]}