# React 組件設計文檔

## 🧩 組件架構

### 組件分類
1. **Layout Components** - 布局組件
2. **Common Components** - 通用組件  
3. **Page Components** - 頁面組件
4. **UI Components** - UI基礎組件

## 📐 Layout Components

### 1. Header
```jsx
// components/layout/Header.jsx
<Header>
  <Logo />
  <Navigation />
  <ThemeToggle />
  <MobileMenuButton />
</Header>
```

**功能**:
- 網站Logo
- 主導航選單
- 主題切換按鈕
- 手機版選單按鈕

**Props**:
- `isScrolled`: boolean - 滾動狀態
- `currentPage`: string - 當前頁面

### 2. Navigation
```jsx
// components/layout/Navigation.jsx
<Navigation>
  <NavItem to="/" label="首頁" />
  <NavItem to="/about" label="關於我" />
  <NavItem to="/skills" label="技能" />
  <NavItem to="/blog" label="部落格" />
  <NavItem to="/contact" label="聯絡" />
</Navigation>
```

**功能**:
- 響應式導航
- 活動狀態指示
- 平滑滾動

### 3. Footer
```jsx
// components/layout/Footer.jsx
<Footer>
  <SocialLinks />
  <Copyright />
  <BackToTop />
</Footer>
```

**功能**:
- 社交媒體連結
- 版權資訊
- 回到頂部按鈕

### 4. Layout
```jsx
// components/layout/Layout.jsx
<Layout>
  <Header />
  <main>{children}</main>
  <Footer />
</Layout>
```

## 🔧 Common Components

### 1. Hero Section
```jsx
// components/common/HeroSection.jsx
<HeroSection>
  <HeroContent>
    <Title />
    <Subtitle />
    <CTAButtons />
  </HeroContent>
  <HeroImage />
</HeroSection>
```

**Props**:
- `title`: string
- `subtitle`: string
- `backgroundImage`: string
- `ctaButtons`: array

### 2. Section Container
```jsx
// components/common/Section.jsx
<Section id="about" className="bg-gray-50">
  <Container>
    <SectionTitle />
    <SectionContent />
  </Container>
</Section>
```

**Props**:
- `id`: string
- `className`: string
- `title`: string
- `children`: ReactNode

### 3. Card
```jsx
// components/common/Card.jsx
<Card variant="elevated" size="medium">
  <CardHeader />
  <CardContent />
  <CardActions />
</Card>
```

**Props**:
- `variant`: 'flat' | 'elevated' | 'outlined'
- `size`: 'small' | 'medium' | 'large'
- `hover`: boolean

### 4. Button
```jsx
// components/ui/Button.jsx
<Button 
  variant="primary" 
  size="medium"
  onClick={handleClick}
>
  點擊我
</Button>
```

**Props**:
- `variant`: 'primary' | 'secondary' | 'outline' | 'ghost'
- `size`: 'small' | 'medium' | 'large'
- `disabled`: boolean
- `loading`: boolean

## 📄 Page Components

### 1. HomePage
```jsx
// pages/HomePage.jsx
<HomePage>
  <HeroSection />
  <AboutPreview />
  <SkillsPreview />
  <RecentBlog />
  <ContactCTA />
</HomePage>
```

### 2. AboutPage
```jsx
// pages/AboutPage.jsx
<AboutPage>
  <PersonalInfo />
  <Timeline />
  <Interests />
</AboutPage>
```

### 3. SkillsPage
```jsx
// pages/SkillsPage.jsx
<SkillsPage>
  <SkillCategories />
  <SkillGrid />
  <Certifications />
</SkillsPage>
```

### 4. BlogPage
```jsx
// pages/BlogPage.jsx
<BlogPage>
  <BlogHeader />
  <BlogFilters />
  <BlogGrid />
  <Pagination />
</BlogPage>
```

### 5. ContactPage
```jsx
// pages/ContactPage.jsx
<ContactPage>
  <ContactInfo />
  <ContactForm />
  <SocialLinks />
</ContactPage>
```

## 🎨 UI Components

### 1. SkillBar
```jsx
// components/ui/SkillBar.jsx
<SkillBar 
  skill="React" 
  level={90} 
  color="#61dafb"
  animated={true}
/>
```

### 2. Timeline
```jsx
// components/ui/Timeline.jsx
<Timeline>
  <TimelineItem 
    date="2023"
    title="前端工程師"
    description="..."
  />
</Timeline>
```

### 3. ContactForm
```jsx
// components/ui/ContactForm.jsx
<ContactForm onSubmit={handleSubmit}>
  <FormField name="name" label="姓名" required />
  <FormField name="email" label="電子郵件" type="email" required />
  <FormField name="message" label="訊息" type="textarea" required />
  <SubmitButton />
</ContactForm>
```

### 4. BlogCard
```jsx
// components/ui/BlogCard.jsx
<BlogCard>
  <BlogImage />
  <BlogMeta />
  <BlogTitle />
  <BlogExcerpt />
  <ReadMoreLink />
</BlogCard>
```

### 5. SocialIcon
```jsx
// components/ui/SocialIcon.jsx
<SocialIcon 
  platform="github" 
  url="https://github.com/username"
  size="medium"
/>
```

## 🎭 動畫組件

### 1. FadeIn
```jsx
// components/animations/FadeIn.jsx
<FadeIn delay={0.2} duration={0.6}>
  <Content />
</FadeIn>
```

### 2. SlideIn
```jsx
// components/animations/SlideIn.jsx
<SlideIn direction="left" distance={50}>
  <Content />
</SlideIn>
```

### 3. ScrollReveal
```jsx
// components/animations/ScrollReveal.jsx
<ScrollReveal threshold={0.3}>
  <Content />
</ScrollReveal>
```

## 🔄 狀態管理

### Context Providers
```jsx
// context/ThemeContext.js
const ThemeContext = createContext();

// context/AppContext.js  
const AppContext = createContext();
```

### Custom Hooks
```jsx
// hooks/useTheme.js
const useTheme = () => {
  const context = useContext(ThemeContext);
  return context;
};

// hooks/useScrollPosition.js
const useScrollPosition = () => {
  // 滾動位置邏輯
};

// hooks/useIntersectionObserver.js
const useIntersectionObserver = (options) => {
  // 交集觀察器邏輯
};
```

## 📱 響應式設計

### 斷點使用
```jsx
// utils/breakpoints.js
export const breakpoints = {
  mobile: '(max-width: 767px)',
  tablet: '(min-width: 768px) and (max-width: 1023px)',
  desktop: '(min-width: 1024px)'
};

// hooks/useMediaQuery.js
const useMediaQuery = (query) => {
  // 媒體查詢邏輯
};
```

## 🎯 組件開發優先級

### Phase 1 (核心組件)
1. Layout (Header, Footer, Navigation)
2. Button, Card, Section
3. HeroSection

### Phase 2 (頁面組件)
1. HomePage
2. AboutPage  
3. SkillsPage

### Phase 3 (進階功能)
1. BlogPage
2. ContactPage
3. 動畫組件

## 📋 組件檢查清單

每個組件應該包含:
- [ ] TypeScript 類型定義
- [ ] PropTypes 或 TypeScript Props
- [ ] 響應式設計
- [ ] 可訪問性 (a11y)
- [ ] 單元測試
- [ ] Storybook 文檔 (可選)
