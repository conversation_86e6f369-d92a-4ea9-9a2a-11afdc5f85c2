# 開發指南

## 🚀 快速開始

### 環境要求
- Node.js 18+ 
- npm 或 yarn 或 pnpm
- Git
- VS Code (推薦)

### 初始化項目
```bash
# 使用 Vite 創建 React 項目
npm create vite@latest my-portfolio -- --template react

# 進入項目目錄
cd my-portfolio

# 安裝依賴
npm install

# 啟動開發服務器
npm run dev
```

### 推薦的 VS Code 擴展
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens
- Thunder Client (API 測試)

## 📦 依賴安裝

### 核心依賴
```bash
# 路由
npm install react-router-dom

# 狀態管理 (如果需要)
npm install zustand
# 或者
npm install @reduxjs/toolkit react-redux

# UI 組件庫 (可選)
npm install @headlessui/react
# 或者
npm install @mui/material @emotion/react @emotion/styled

# 圖標
npm install react-icons
npm install lucide-react

# 動畫
npm install framer-motion

# 表單處理
npm install react-hook-form
npm install @hookform/resolvers yup

# HTTP 請求
npm install axios

# 工具庫
npm install clsx
npm install date-fns
```

### 開發依賴
```bash
# CSS 預處理器 (如果使用 SCSS)
npm install -D sass

# Tailwind CSS (如果選擇使用)
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p

# 代碼品質
npm install -D eslint prettier
npm install -D @typescript-eslint/eslint-plugin @typescript-eslint/parser

# 測試
npm install -D vitest @testing-library/react @testing-library/jest-dom
npm install -D @testing-library/user-event

# Git Hooks
npm install -D husky lint-staged
```

## 🏗️ 項目結構設置

### 創建目錄結構
```bash
mkdir -p src/{components/{common,layout,ui},pages,hooks,context,utils,assets/{images,icons},styles,data}
```

### 文件結構
```
src/
├── components/
│   ├── common/
│   │   ├── HeroSection.jsx
│   │   ├── Section.jsx
│   │   └── Card.jsx
│   ├── layout/
│   │   ├── Header.jsx
│   │   ├── Footer.jsx
│   │   ├── Navigation.jsx
│   │   └── Layout.jsx
│   └── ui/
│       ├── Button.jsx
│       ├── Input.jsx
│       ├── Modal.jsx
│       └── index.js
├── pages/
│   ├── HomePage.jsx
│   ├── AboutPage.jsx
│   ├── SkillsPage.jsx
│   ├── BlogPage.jsx
│   └── ContactPage.jsx
├── hooks/
│   ├── useTheme.js
│   ├── useScrollPosition.js
│   └── useMediaQuery.js
├── context/
│   ├── ThemeContext.jsx
│   └── AppContext.jsx
├── utils/
│   ├── constants.js
│   ├── helpers.js
│   └── api.js
├── assets/
│   ├── images/
│   └── icons/
├── styles/
│   ├── globals.css
│   ├── variables.css
│   └── components/
└── data/
    ├── skills.js
    ├── projects.js
    └── blog.js
```

## ⚙️ 配置文件

### ESLint 配置 (.eslintrc.js)
```javascript
module.exports = {
  env: {
    browser: true,
    es2021: true,
  },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: 12,
    sourceType: 'module',
  },
  plugins: ['react', '@typescript-eslint'],
  rules: {
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
  },
  settings: {
    react: {
      version: 'detect',
    },
  },
};
```

### Prettier 配置 (.prettierrc)
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### Vite 配置 (vite.config.js)
```javascript
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@hooks': path.resolve(__dirname, './src/hooks'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@styles': path.resolve(__dirname, './src/styles'),
    },
  },
  server: {
    port: 3000,
    open: true,
  },
});
```

## 🧪 測試設置

### Vitest 配置 (vitest.config.js)
```javascript
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.js'],
  },
});
```

### 測試設置文件 (src/test/setup.js)
```javascript
import '@testing-library/jest-dom';
```

### 範例測試文件
```javascript
// src/components/ui/Button.test.jsx
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Button from './Button';

describe('Button', () => {
  test('renders button with text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  test('calls onClick when clicked', async () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    await userEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

## 📝 開發工作流程

### Git 工作流程
```bash
# 創建新功能分支
git checkout -b feature/hero-section

# 提交變更
git add .
git commit -m "feat: add hero section component"

# 推送到遠程
git push origin feature/hero-section

# 合併到主分支
git checkout main
git merge feature/hero-section
```

### 提交訊息規範
```
feat: 新功能
fix: 修復 bug
docs: 文檔更新
style: 代碼格式化
refactor: 重構
test: 測試相關
chore: 建構工具或輔助工具的變動
```

### 開發檢查清單
- [ ] 組件功能正常
- [ ] 響應式設計正確
- [ ] 可訪問性檢查
- [ ] 代碼格式化
- [ ] ESLint 檢查通過
- [ ] 測試覆蓋率足夠
- [ ] 性能檢查

## 🚀 部署指南

### 建構項目
```bash
# 建構生產版本
npm run build

# 預覽建構結果
npm run preview
```

### Vercel 部署
```bash
# 安裝 Vercel CLI
npm install -g vercel

# 部署
vercel

# 設置自定義域名
vercel --prod
```

### Netlify 部署
```bash
# 安裝 Netlify CLI
npm install -g netlify-cli

# 建構並部署
npm run build
netlify deploy --prod --dir=dist
```

### GitHub Pages 部署
```bash
# 安裝 gh-pages
npm install -D gh-pages

# 在 package.json 添加腳本
"scripts": {
  "deploy": "gh-pages -d dist"
}

# 部署
npm run build
npm run deploy
```

## 🔧 開發技巧

### 常用代碼片段
```javascript
// React 函數組件
const ComponentName = ({ prop1, prop2 }) => {
  return (
    <div>
      {/* 組件內容 */}
    </div>
  );
};

// 自定義 Hook
const useCustomHook = () => {
  const [state, setState] = useState(initialValue);
  
  useEffect(() => {
    // 副作用邏輯
  }, []);
  
  return { state, setState };
};

// Context Provider
const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light');
  
  const value = {
    theme,
    setTheme,
  };
  
  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
```

### 性能優化技巧
- 使用 React.memo 避免不必要的重渲染
- 使用 useMemo 和 useCallback 優化計算
- 實現代碼分割和懶加載
- 優化圖片和資源加載
- 使用 Web Vitals 監控性能

### 調試技巧
- 使用 React Developer Tools
- 使用 console.log 和 debugger
- 使用 VS Code 調試器
- 使用 Network 面板檢查請求
- 使用 Lighthouse 檢查性能

## 📚 學習資源

### 官方文檔
- [React 官方文檔](https://react.dev/)
- [Vite 官方文檔](https://vitejs.dev/)
- [React Router 文檔](https://reactrouter.com/)

### 推薦教程
- React 官方教程
- Scrimba React 課程
- FreeCodeCamp React 教程

### 社群資源
- React 官方 Discord
- Stack Overflow
- GitHub Discussions

## 🎯 下一步計劃

1. **完成基礎設置** ✅
2. **開發核心組件**
3. **實現頁面功能**
4. **添加動畫效果**
5. **優化性能**
6. **部署上線**

記住：先讓功能正常運作，再進行優化！
