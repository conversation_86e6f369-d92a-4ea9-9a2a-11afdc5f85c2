# CSS 架構與設計系統

## 🎨 設計系統

### 色彩系統
```css
/* colors.css */
:root {
  /* Primary Colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;  /* 主色 */
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Secondary Colors */
  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;  /* 次色 */
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  /* Accent Colors */
  --color-accent-50: #fffbeb;
  --color-accent-100: #fef3c7;
  --color-accent-200: #fde68a;
  --color-accent-300: #fcd34d;
  --color-accent-400: #fbbf24;
  --color-accent-500: #f59e0b;  /* 強調色 */
  --color-accent-600: #d97706;
  --color-accent-700: #b45309;
  --color-accent-800: #92400e;
  --color-accent-900: #78350f;

  /* Semantic Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* Neutral Colors */
  --color-white: #ffffff;
  --color-black: #000000;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
}
```

### 字體系統
```css
/* typography.css */
:root {
  /* Font Families */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-heading: 'Poppins', 'Inter', sans-serif;
  --font-mono: 'Fira Code', 'Monaco', 'Cascadia Code', monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  --text-6xl: 3.75rem;    /* 60px */

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
}
```

### 間距系統
```css
/* spacing.css */
:root {
  /* Spacing Scale (基於 8px 網格) */
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  --space-32: 8rem;     /* 128px */

  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}
```

### 陰影與邊框
```css
/* shadows.css */
:root {
  /* Box Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;   /* 2px */
  --radius-base: 0.25rem;  /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-full: 9999px;

  /* Border Widths */
  --border-0: 0;
  --border-1: 1px;
  --border-2: 2px;
  --border-4: 4px;
  --border-8: 8px;
}
```

## 🏗️ CSS 架構

### 文件結構
```
src/styles/
├── base/
│   ├── reset.css          # CSS Reset
│   ├── typography.css     # 字體樣式
│   └── global.css         # 全域樣式
├── tokens/
│   ├── colors.css         # 色彩變數
│   ├── spacing.css        # 間距變數
│   ├── shadows.css        # 陰影變數
│   └── breakpoints.css    # 斷點變數
├── components/
│   ├── button.css         # 按鈕樣式
│   ├── card.css           # 卡片樣式
│   ├── form.css           # 表單樣式
│   └── navigation.css     # 導航樣式
├── layout/
│   ├── header.css         # 頁首樣式
│   ├── footer.css         # 頁尾樣式
│   └── grid.css           # 網格系統
├── pages/
│   ├── home.css           # 首頁樣式
│   ├── about.css          # 關於頁面
│   └── contact.css        # 聯絡頁面
├── utilities/
│   ├── animations.css     # 動畫工具類
│   ├── helpers.css        # 輔助類
│   └── responsive.css     # 響應式工具
└── main.css              # 主要樣式文件
```

### 主要樣式文件
```css
/* main.css */
@import './tokens/colors.css';
@import './tokens/spacing.css';
@import './tokens/shadows.css';
@import './tokens/breakpoints.css';

@import './base/reset.css';
@import './base/typography.css';
@import './base/global.css';

@import './layout/grid.css';
@import './layout/header.css';
@import './layout/footer.css';

@import './components/button.css';
@import './components/card.css';
@import './components/form.css';
@import './components/navigation.css';

@import './utilities/animations.css';
@import './utilities/helpers.css';
@import './utilities/responsive.css';
```

## 📱 響應式設計

### 斷點系統
```css
/* breakpoints.css */
:root {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Media Query Mixins (如果使用 SCSS) */
@mixin mobile {
  @media (max-width: 767px) { @content; }
}

@mixin tablet {
  @media (min-width: 768px) and (max-width: 1023px) { @content; }
}

@mixin desktop {
  @media (min-width: 1024px) { @content; }
}
```

### 響應式工具類
```css
/* responsive.css */
/* 顯示/隱藏 */
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.grid { display: grid; }

/* 響應式顯示 */
@media (max-width: 767px) {
  .hidden-mobile { display: none !important; }
  .block-mobile { display: block !important; }
}

@media (min-width: 768px) {
  .hidden-tablet { display: none !important; }
  .block-tablet { display: block !important; }
}

@media (min-width: 1024px) {
  .hidden-desktop { display: none !important; }
  .block-desktop { display: block !important; }
}
```

## 🎭 動畫系統

### 動畫變數
```css
/* animations.css */
:root {
  /* Timing Functions */
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  /* Durations */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;
}

/* 基礎動畫 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 動畫工具類 */
.animate-fade-in {
  animation: fadeIn var(--duration-300) var(--ease-out);
}

.animate-slide-up {
  animation: slideInUp var(--duration-500) var(--ease-out);
}

.animate-slide-left {
  animation: slideInLeft var(--duration-500) var(--ease-out);
}
```

## 🧩 組件樣式範例

### Button 組件
```css
/* components/button.css */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: var(--border-1) solid transparent;
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-weight: var(--font-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-in-out);
}

.btn:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* Sizes */
.btn--sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
}

.btn--md {
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
}

.btn--lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-lg);
}

/* Variants */
.btn--primary {
  background-color: var(--color-primary-600);
  color: var(--color-white);
}

.btn--primary:hover {
  background-color: var(--color-primary-700);
}

.btn--secondary {
  background-color: var(--color-secondary-100);
  color: var(--color-secondary-900);
}

.btn--outline {
  border-color: var(--color-primary-600);
  color: var(--color-primary-600);
  background-color: transparent;
}
```

### Card 組件
```css
/* components/card.css */
.card {
  background-color: var(--color-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all var(--duration-200) var(--ease-in-out);
}

.card--elevated {
  box-shadow: var(--shadow-md);
}

.card--elevated:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card__header {
  padding: var(--space-6);
  border-bottom: var(--border-1) solid var(--color-gray-200);
}

.card__content {
  padding: var(--space-6);
}

.card__actions {
  padding: var(--space-4) var(--space-6);
  background-color: var(--color-gray-50);
}
```

## 🎯 最佳實踐

### CSS 命名規範
- 使用 BEM 方法論
- 組件名稱使用 kebab-case
- 修飾符使用 -- 分隔
- 元素使用 __ 分隔

### 性能優化
- 使用 CSS 變數減少重複
- 避免深層嵌套選擇器
- 使用 transform 和 opacity 進行動畫
- 合理使用 will-change 屬性

### 可維護性
- 模組化 CSS 文件
- 使用語義化的變數名稱
- 保持一致的間距和色彩系統
- 文檔化設計決策
