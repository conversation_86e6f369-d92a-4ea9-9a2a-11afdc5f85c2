{"name": "chan-chun-yin-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"axios": "^1.6.2", "bootstrap": "^5.3.7", "clsx": "^2.0.0", "framer-motion": "^10.16.5", "react": "^18.2.0", "react-bootstrap": "^2.10.10", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-icons": "^4.12.0", "react-router-dom": "^6.20.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "globals": "^15.9.0", "jsdom": "^23.0.1", "vite": "^5.0.0", "vitest": "^1.0.0"}}